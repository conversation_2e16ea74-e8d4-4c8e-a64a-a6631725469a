#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试多模态图片上传和处理功能
验证所有新增的图片处理能力
"""

import sys
import os
import tempfile
import base64
from PIL import Image
import io

# 添加项目路径
sys.path.append('src')

def create_test_image():
    """创建测试图片"""
    try:
        # 创建一个简单的测试图片
        img = Image.new('RGB', (200, 200), color='red')
        
        # 保存到临时文件
        temp_file = tempfile.NamedTemporaryFile(suffix='.jpg', delete=False)
        img.save(temp_file.name, 'JPEG')
        temp_file.close()
        
        return temp_file.name
    except Exception as e:
        print(f"创建测试图片失败: {e}")
        return None

def test_vision_model_detection():
    """测试视觉模型检测功能"""
    print("🔍 测试视觉模型检测功能...")
    print("=" * 50)
    
    try:
        from llamafactory.webui.components.simple_assistant import is_vision_model, VISION_MODELS
        
        print(f"📊 支持视觉功能的服务商数量: {len(VISION_MODELS)}")
        
        # 测试各服务商的视觉模型检测
        test_cases = [
            ("OpenAI", "gpt-4o", True),
            ("OpenAI", "gpt-4-vision-preview", True),
            ("OpenAI", "gpt-3.5-turbo", False),
            ("Claude", "claude-3-5-sonnet-20241022", True),
            ("Claude", "claude-3-haiku-20240307", False),
            ("Google Gemini", "gemini-pro-vision", True),
            ("Google Gemini", "gemini-pro", True),
            ("OpenRouter", "gpt-4o", True),
            ("Poe", "GPT-4-Turbo", True),
            ("智谱AI", "glm-4", False),  # 智谱AI暂不支持视觉
        ]
        
        for provider, model, expected in test_cases:
            result = is_vision_model(provider, model)
            status = "✅" if result == expected else "❌"
            print(f"   {status} {provider} - {model}: {'支持' if result else '不支持'}视觉")
        
        print("\n📋 支持视觉功能的服务商:")
        for provider, models in VISION_MODELS.items():
            print(f"   {provider}: {len(models)}个模型")
            for model in models[:3]:  # 只显示前3个
                print(f"     - {model}")
            if len(models) > 3:
                print(f"     - ... 等{len(models)}个模型")
        
        print("✅ 视觉模型检测功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 视觉模型检测功能测试失败: {e}")
        return False

def test_image_processing():
    """测试图片处理功能"""
    print("\n🖼️ 测试图片处理功能...")
    print("=" * 50)
    
    try:
        from llamafactory.webui.components.simple_assistant import process_image, SUPPORTED_IMAGE_FORMATS, MAX_IMAGE_SIZE
        
        print(f"📋 支持的图片格式: {', '.join(SUPPORTED_IMAGE_FORMATS)}")
        print(f"📏 最大文件大小: {MAX_IMAGE_SIZE // (1024*1024)}MB")
        
        # 创建测试图片
        test_image_path = create_test_image()
        if not test_image_path:
            print("❌ 无法创建测试图片")
            return False
        
        print(f"📷 测试图片路径: {test_image_path}")
        
        # 测试图片处理
        result = process_image(test_image_path)
        
        if "error" in result:
            print(f"❌ 图片处理失败: {result['error']}")
            return False
        
        print("✅ 图片处理成功:")
        print(f"   格式: {result['format']}")
        print(f"   尺寸: {result['size']}")
        print(f"   文件大小: {result['file_size']} bytes")
        print(f"   原始文件名: {result['original_name']}")
        print(f"   Base64长度: {len(result['base64'])} 字符")
        
        # 清理测试文件
        os.unlink(test_image_path)
        
        print("✅ 图片处理功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 图片处理功能测试失败: {e}")
        return False

def test_multimodal_message_creation():
    """测试多模态消息创建"""
    print("\n💬 测试多模态消息创建...")
    print("=" * 50)
    
    try:
        from llamafactory.webui.components.simple_assistant import create_image_message
        
        # 模拟图片数据
        test_images = [{
            "base64": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
            "format": "png",
            "size": (1, 1),
            "file_size": 100,
            "original_name": "test.png"
        }]
        
        test_text = "请分析这张图片"
        
        # 测试不同服务商的消息格式
        providers = ["OpenAI", "Claude", "Google Gemini", "OpenRouter", "Poe"]
        
        for provider in providers:
            print(f"📋 测试{provider}消息格式:")
            messages = create_image_message(test_text, test_images, provider)
            
            if messages:
                print(f"   ✅ 消息数量: {len(messages)}")
                print(f"   ✅ 消息结构: {list(messages[0].keys())}")
                
                # 检查内容结构
                content = messages[0].get("content", messages[0].get("parts", []))
                if isinstance(content, list):
                    print(f"   ✅ 内容项数: {len(content)}")
                else:
                    print(f"   ✅ 内容类型: {type(content).__name__}")
            else:
                print("   ❌ 消息创建失败")
        
        print("✅ 多模态消息创建测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 多模态消息创建测试失败: {e}")
        return False

def test_api_call_with_images():
    """测试带图片的API调用"""
    print("\n🔗 测试带图片的API调用...")
    print("=" * 50)
    
    try:
        from llamafactory.webui.components.simple_assistant import call_ai_api
        
        # 测试配置
        test_config = {
            "selected_provider": "OpenAI",
            "model_id": "gpt-4o",
            "api_keys": {"OpenAI": "sk-test-key"},
            "temperature": 0.7,
            "max_tokens": 100,
            "timeout": 30
        }
        
        # 模拟图片数据
        test_images = [{
            "base64": "test-base64-data",
            "format": "jpeg",
            "size": (200, 200),
            "file_size": 1000,
            "original_name": "test.jpg"
        }]
        
        test_messages = [
            {"role": "system", "content": "你是一个AI助手。"},
            {"role": "user", "content": "请分析这张图片"}
        ]
        
        print("📋 测试支持视觉的模型:")
        response = call_ai_api(test_messages, test_config, images=test_images)
        print(f"   响应: {response[:100]}...")
        
        if "不支持图片功能" in response:
            print("   ❌ 模型不支持图片功能")
        elif "API密钥" in response:
            print("   ✅ 图片支持检查通过（API密钥问题）")
        else:
            print("   ✅ API调用格式正确")
        
        print("\n📋 测试不支持视觉的模型:")
        test_config["model_id"] = "gpt-3.5-turbo"
        response = call_ai_api(test_messages, test_config, images=test_images)
        print(f"   响应: {response[:100]}...")
        
        if "不支持图片功能" in response:
            print("   ✅ 正确识别不支持图片的模型")
        else:
            print("   ⚠️ 未正确识别模型限制")
        
        print("✅ 带图片的API调用测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 带图片的API调用测试失败: {e}")
        return False

def check_web_ui_multimodal():
    """检查Web UI多模态功能"""
    print("\n🌐 检查Web UI多模态功能...")
    print("=" * 50)
    
    try:
        import requests
        response = requests.get("http://127.0.0.1:7860", timeout=5)
        if response.status_code == 200:
            print("✅ Web UI正在运行")
            print("\n🎯 新增多模态功能:")
            print("1. 📷 图片上传组件 - 支持拖拽和点击上传")
            print("2. 🖼️ 图片预览画廊 - 显示上传的图片")
            print("3. 🔍 视觉模型检测 - 自动识别模型是否支持图片")
            print("4. 💬 多模态对话 - 图片+文字组合消息")
            print("5. 📊 上传状态反馈 - 实时显示处理状态")
            
            print("\n💡 使用方法:")
            print("• 进入'💬 智能对话'标签页")
            print("• 选择支持视觉的AI模型（如gpt-4o, claude-3-5-sonnet）")
            print("• 点击'📷 图片模式'或展开图片上传区域")
            print("• 上传图片文件（JPG, PNG, GIF等）")
            print("• 输入问题并发送，AI将分析图片内容")
            
            return True
        else:
            print(f"⚠️ Web UI响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"⚠️ 无法连接到Web UI: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("🤖 多模态图片功能测试")
    print("=" * 60)
    
    tests = [
        test_vision_model_detection,
        test_image_processing,
        test_multimodal_message_creation,
        test_api_call_with_images
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 功能测试结果: {passed}/{total} 通过")
    
    # 检查Web UI功能
    web_ui_running = check_web_ui_multimodal()
    
    print("\n" + "=" * 60)
    
    if passed == total:
        print("🎉 多模态图片功能测试全部通过！")
        
        if web_ui_running:
            print("\n🌟 多模态功能添加完成！")
            print("📍 访问 http://127.0.0.1:7860")
            print("🎯 进入'🤖 AI助手' → '💬 智能对话'")
            
            print("\n✨ 功能特性总结:")
            print("🔧 支持的图片格式: JPG, PNG, GIF, WebP, BMP")
            print("📏 文件大小限制: 最大10MB")
            print("📊 图片数量限制: 最多10张")
            print("🤖 支持的AI模型: 5个服务商的视觉模型")
            print("💬 多模态对话: 图片+文字组合")
        else:
            print("\n⚠️ 请启动Web UI: python src/webui.py")
        
        return True
    else:
        print(f"⚠️ 有 {total - passed} 个测试失败")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    
    if success:
        print("\n🌟 多模态图片功能添加完成！")
        print("现在您可以上传图片并与AI进行多模态对话了")
    else:
        print("\n🛑 部分功能可能需要进一步调试")
