#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复后的AI配置
"""

import sys
import json
sys.path.append('src')

def test_config():
    """测试配置"""
    print("🔍 测试修复后的AI配置...")
    
    # 读取配置
    try:
        with open("simple_assistant_config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"❌ 读取配置失败: {e}")
        return
    
    print("📊 当前配置:")
    print(f"   服务商: {config.get('selected_provider')}")
    print(f"   预设模型: {config.get('selected_model')}")
    print(f"   自定义模型: '{config.get('custom_model_name')}'")
    print(f"   API密钥: {list(config.get('api_keys', {}).keys())}")
    
    # 检查配置
    provider = config.get('selected_provider')
    api_keys = config.get('api_keys', {})
    custom_model = config.get('custom_model_name', '')
    
    issues = []
    
    if provider not in api_keys:
        issues.append(f"❌ 服务商'{provider}'没有API密钥")
    
    if provider == "Google Gemini" and custom_model == "gemini-2.5-flash":
        issues.append("❌ 自定义模型'gemini-2.5-flash'不存在")
    
    if issues:
        print("\n🚨 发现问题:")
        for issue in issues:
            print(f"   {issue}")
        return False
    else:
        print("\n✅ 配置检查通过")
    
    # 测试API连接
    print("\n🔗 测试API连接...")
    try:
        from llamafactory.webui.components.simple_assistant import call_ai_api
        
        test_messages = [
            {"role": "system", "content": "你是一个AI助手，请简短回复。"},
            {"role": "user", "content": "请回复'连接测试成功'"}
        ]
        
        response = call_ai_api(test_messages, config)
        print(f"API响应: {response[:100]}...")
        
        if "❌" in response:
            print("❌ 连接测试失败")
            print(f"错误信息: {response}")
            return False
        elif len(response.strip()) > 0:
            print("✅ 连接测试成功！")
            return True
        else:
            print("⚠️ 收到空响应")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

if __name__ == "__main__":
    if test_config():
        print("\n🎉 配置修复成功！现在可以正常使用AI对话功能了")
        print("\n💡 使用步骤:")
        print("1. 刷新Web UI页面")
        print("2. 进入'🤖 AI助手' → '⚙️ AI模型配置'")
        print("3. 点击'🔗 测试API连接'验证")
        print("4. 切换到'💬 智能对话'开始聊天")
    else:
        print("\n❌ 配置仍有问题，请检查API密钥是否正确")
