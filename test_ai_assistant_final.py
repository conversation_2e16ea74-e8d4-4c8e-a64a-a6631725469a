#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终AI助手功能测试脚本
验证修复后的智能助手是否正常工作
"""

import sys
import os
import time

# 添加项目路径
sys.path.append('src')

def test_assistant_import():
    """测试AI助手导入"""
    print("🔍 测试AI助手模块导入...")
    
    try:
        from llamafactory.webui.components.simple_assistant import (
            create_simple_assistant,
            get_hardware_recommendations,
            analyze_training_log,
            load_config,
            save_config
        )
        print("✅ AI助手模块导入成功")
        return True
    except Exception as e:
        print(f"❌ AI助手模块导入失败: {e}")
        return False

def test_hardware_recommendations():
    """测试硬件推荐功能"""
    print("\n🔧 测试硬件推荐功能...")
    
    try:
        from llamafactory.webui.components.simple_assistant import get_hardware_recommendations
        
        # 测试不同显存大小
        test_cases = [4, 8, 16, 24]
        
        for gpu_memory in test_cases:
            recommendations = get_hardware_recommendations(gpu_memory)
            print(f"📊 {gpu_memory}GB显存推荐:")
            print(f"   长度: {len(recommendations)}字符")
            print(f"   包含配置: {'quantization_bit' in recommendations}")
            print(f"   包含建议: {'建议' in recommendations}")
        
        print("✅ 硬件推荐功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 硬件推荐功能测试失败: {e}")
        return False

def test_log_analysis():
    """测试日志分析功能"""
    print("\n📊 测试日志分析功能...")
    
    try:
        from llamafactory.webui.components.simple_assistant import analyze_training_log
        
        # 测试不同类型的日志
        test_logs = [
            ("显存不足", "RuntimeError: CUDA out of memory. Tried to allocate 2.00 GiB"),
            ("NaN值问题", "Training loss became nan at step 100"),
            ("正常训练", "Training step 100, loss: 2.345, lr: 1e-4"),
            ("空日志", "")
        ]
        
        for log_type, log_content in test_logs:
            analysis = analyze_training_log(log_content)
            print(f"🔍 {log_type}分析:")
            print(f"   输入: {log_content[:50]}...")
            print(f"   输出长度: {len(analysis)}字符")
            print(f"   包含解决方案: {'解决方案' in analysis or '建议' in analysis}")
        
        print("✅ 日志分析功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 日志分析功能测试失败: {e}")
        return False

def test_config_management():
    """测试配置管理功能"""
    print("\n⚙️ 测试配置管理功能...")
    
    try:
        from llamafactory.webui.components.simple_assistant import load_config, save_config
        
        # 测试加载默认配置
        config = load_config()
        print(f"   默认模型: {config.get('model_name', 'N/A')}")
        print(f"   温度参数: {config.get('temperature', 'N/A')}")
        print(f"   最大Token: {config.get('max_tokens', 'N/A')}")
        
        # 测试保存配置
        test_config = {
            "model_name": "test-model",
            "api_key": "test-key",
            "temperature": 0.8,
            "max_tokens": 1500
        }
        
        save_config(test_config)
        
        # 验证保存
        loaded_config = load_config()
        assert loaded_config["model_name"] == "test-model"
        assert loaded_config["temperature"] == 0.8
        
        print("✅ 配置管理功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置管理功能测试失败: {e}")
        return False

def test_assistant_creation():
    """测试AI助手创建"""
    print("\n🤖 测试AI助手组件创建...")
    
    try:
        from llamafactory.webui.components.simple_assistant import create_simple_assistant
        
        # 创建助手组件
        assistant_components = create_simple_assistant()
        
        print(f"   返回组件数量: {len(assistant_components)}")
        print(f"   组件类型: {type(assistant_components)}")
        
        print("✅ AI助手组件创建测试通过")
        return True
        
    except Exception as e:
        print(f"❌ AI助手组件创建测试失败: {e}")
        return False

def test_interface_integration():
    """测试界面集成"""
    print("\n🖥️ 测试界面集成...")
    
    try:
        from llamafactory.webui.components import create_smart_assistant
        
        # 测试导入是否正确
        assistant_components = create_smart_assistant()
        
        print(f"   集成组件数量: {len(assistant_components)}")
        print(f"   集成成功: {isinstance(assistant_components, dict)}")
        
        print("✅ 界面集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 界面集成测试失败: {e}")
        return False

def check_web_ui_status():
    """检查Web UI状态"""
    print("\n🌐 检查Web UI状态...")
    
    try:
        import requests
        response = requests.get("http://127.0.0.1:7860", timeout=5)
        if response.status_code == 200:
            print("✅ Web UI正在运行")
            print("🎯 AI助手应该在'🤖 AI助手'标签页中可见")
            return True
        else:
            print(f"⚠️ Web UI响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"⚠️ 无法连接到Web UI: {e}")
        print("💡 请确保Web UI正在运行 (python src/webui.py)")
        return False

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始AI助手最终功能测试...")
    print("=" * 60)
    
    tests = [
        test_assistant_import,
        test_hardware_recommendations,
        test_log_analysis,
        test_config_management,
        test_assistant_creation,
        test_interface_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 功能测试结果: {passed}/{total} 通过")
    
    # 检查Web UI状态
    web_ui_running = check_web_ui_status()
    
    print("\n" + "=" * 60)
    
    if passed == total:
        print("🎉 所有功能测试通过！")
        print("\n📋 AI助手功能清单:")
        print("✅ 模块导入正常")
        print("✅ 硬件配置推荐")
        print("✅ 训练日志分析")
        print("✅ 配置管理")
        print("✅ 组件创建")
        print("✅ 界面集成")
        
        if web_ui_running:
            print("\n🎊 AI助手已完全修复并正常运行！")
            print("🌟 访问 http://127.0.0.1:7860")
            print("📍 点击'🤖 AI助手'标签页开始使用")
            
            print("\n💡 使用指南:")
            print("1. 💬 智能对话 - 点击快捷问题或输入自定义问题")
            print("2. 🛠️ 训练工具 - 获取硬件推荐和日志分析")
            print("3. 所有功能都可以正常使用，无需额外配置")
        else:
            print("\n⚠️ 请启动Web UI: python src/webui.py")
        
        return True
    else:
        print(f"⚠️ 有 {total - passed} 个测试失败")
        return False

def cleanup_test_files():
    """清理测试文件"""
    try:
        test_files = ["simple_assistant_config.json"]
        for file in test_files:
            if os.path.exists(file):
                os.remove(file)
        print("\n🧹 测试文件清理完成")
    except:
        pass

if __name__ == "__main__":
    print("🤖 LLaMA-Factory AI助手最终测试")
    print("=" * 60)
    
    # 运行测试
    success = run_all_tests()
    
    if success:
        print("\n🌟 测试完成！AI助手已完全修复")
        print("📖 所有功能都已验证正常工作")
    else:
        print("\n🛑 部分测试失败，请检查相关功能")
    
    # 清理测试文件
    cleanup_test_files()
