# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

LLaMA-Factory is an open-source framework for fine-tuning 100+ large language models with unified interfaces. It's organized as a Python package that supports multiple training methods (LoRA, QLoRA, full fine-tuning), inference engines (HuggingFace, vLLM, SGLang), and provides CLI, web UI, and API interfaces.

## Development Commands

### Environment Setup
```bash
# Install from source (recommended for development)
pip install -e ".[torch,metrics]" --no-build-isolation
```

### Core Development Commands
```bash
# Build package
make build

# Run tests (disables CUDA and W&B)
make test

# Check code quality (Ruff linting)
make quality

# Auto-fix code style
make style

# Install and run pre-commit hooks
make commit

# Check license headers
make license
```

### CLI Interface (llamafactory-cli)
```bash
# Training (automatically detects distributed setup)
llamafactory-cli train --config config.yaml

# Interactive chat
llamafactory-cli chat --model_name llama3

# Start API server (OpenAI-compatible)
llamafactory-cli api --model_name llama3

# Model evaluation
llamafactory-cli eval --config eval_config.yaml

# Export/merge LoRA adapters
llamafactory-cli export --config export_config.yaml

# Web interfaces
llamafactory-cli webui    # Full LlamaBoard interface
llamafactory-cli webchat # Simple chat interface
```

## Architecture Overview

### Core Package Structure
- `src/llamafactory/` - Main package with modular architecture
  - `train/` - Training implementations (SFT, DPO, PPO, etc.)
  - `model/` - Model loading, adapters, utilities
  - `data/` - Data processing pipeline with Alpaca/ShareGPT format support
  - `api/` - OpenAI-compatible API server
  - `chat/` - Chat implementations for different inference engines
  - `webui/` - Gradio-based web interface
  - `hparams/` - Hyperparameter management system
  - `eval/` - Model evaluation framework

### Key Configuration Files
- `data/dataset_info.json` - Centralized dataset registry
- `pyproject.toml` - Modern Python packaging with Ruff configuration
- `setup.py` - Package setup with extensive extra dependencies
- `requirements.txt` - Core dependencies

### Training Architecture
The framework supports multiple training stages and methods:
- **Stages**: Pre-training → SFT → Reward Modeling → PPO/DPO/KTO
- **Methods**: Full fine-tuning, Freeze-tuning, LoRA, QLoRA, GaLore, etc.
- **Distributed**: Automatic multi-GPU training with torchrun detection

### Inference Engines
Three inference backends with different performance characteristics:
- **HuggingFace Transformers**: Default, full compatibility
- **vLLM**: High-performance (270% speed improvement)
- **SGLang**: Alternative high-performance backend

## Testing Strategy

### Test Structure
- `tests/data/` - Data processing tests
- `tests/e2e/` - End-to-end training/inference tests
- `tests/model/` - Model loading and utility tests
- `tests/train/` - Training method tests
- `tests/eval/` - Evaluation framework tests

### CI/CD Pipeline
Tests run across Ubuntu/Windows/macOS with Python 3.9-3.12, including backward compatibility with multiple transformers versions. Quality gates include style, linting, license validation, build, and pytest.

## Data System

Dataset configuration uses `data/dataset_info.json` registry with support for:
- **Formats**: Alpaca and ShareGPT with flexible column mapping
- **Types**: Text, multimodal (images/videos/audio), preference data
- **Loading**: Automatic format detection and preprocessing

## Environment Variables

Key environment variables for development:
- `USE_MODELSCOPE_HUB=1` - Use ModelScope hub instead of HuggingFace
- `DISABLE_VERSION_CHECK=1` - Skip version compatibility checks
- `FORCE_TORCHRUN=1` - Force distributed training mode
- `API_HOST`/`API_PORT` - API server configuration

## Hardware Considerations

The framework supports diverse hardware configurations:
- **Memory**: 4GB to 1200GB+ depending on model size and method
- **Quantization**: 2/3/4/5/6/8-bit via multiple backends
- **Accelerators**: CUDA, NPU (Ascend), ROCm (AMD)
- **Distributed**: Automatic multi-GPU detection and setup