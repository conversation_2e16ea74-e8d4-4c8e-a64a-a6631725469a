# 医疗问答模型训练配置
# 适用于：医疗领域问答、诊断辅助
# 特点：高精度要求，专业术语处理

### 模型配置
model_name_or_path: Qwen/Qwen2.5-7B-Instruct  # 推荐使用Qwen或LLaMA
trust_remote_code: true

### 训练方法
stage: sft
do_train: true
finetuning_type: lora

### LoRA配置 (医疗任务推荐较高rank)
lora_rank: 32  # 医疗任务需要更强表达能力
lora_alpha: 64
lora_target: all
lora_dropout: 0.05  # 较低dropout保持稳定性
use_dora: true  # 使用DoRA提升效果

### 数据集配置
dataset: medical_qa_dataset  # 替换为您的医疗数据集
template: qwen
cutoff_len: 3072  # 医疗文本通常较长
max_samples: null  # 使用全部医疗数据
overwrite_cache: true
preprocessing_num_workers: 16

### 训练参数 (医疗任务优化)
per_device_train_batch_size: 2
gradient_accumulation_steps: 8
learning_rate: 5e-5  # 较低学习率保证稳定性
num_train_epochs: 5.0  # 更多轮数确保充分学习
lr_scheduler_type: cosine
warmup_ratio: 0.1
bf16: true

### 正则化 (防止过拟合)
weight_decay: 0.05  # 较高权重衰减
max_grad_norm: 0.5  # 较严格的梯度裁剪
label_smoothing_factor: 0.1

### 输出配置
output_dir: saves/medical_qa_model
logging_steps: 10
save_steps: 200  # 更频繁保存
save_total_limit: 5
plot_loss: true
overwrite_output_dir: true
save_only_model: false

### 评估配置 (重要：医疗模型需要严格评估)
evaluation_strategy: steps
eval_steps: 200
per_device_eval_batch_size: 4
metric_for_best_model: eval_loss
greater_is_better: false
load_best_model_at_end: true

### 数据处理
remove_unused_columns: false
dataloader_num_workers: 8

### 其他配置
report_to: tensorboard  # 建议使用tensorboard监控
ddp_timeout: 180000000

### 医疗特定配置建议
# 1. 数据格式示例：
# {
#   "instruction": "患者症状分析",
#   "input": "患者主诉：头痛、发热、咳嗽3天，体温38.5°C",
#   "output": "根据症状描述，患者可能存在上呼吸道感染，建议进行血常规检查..."
# }

# 2. 安全提示：
# - 训练数据应经过医学专家审核
# - 模型输出仅供参考，不能替代专业医疗诊断
# - 建议在输出中添加免责声明
