# DPO (Direct Preference Optimization) 训练配置
# 适用于：偏好对齐、提升模型回答质量
# 前提：需要先完成SFT训练

### 模型配置
model_name_or_path: saves/qwen2.5-7b/sft  # SFT训练后的模型路径
trust_remote_code: true

### 训练方法
stage: dpo
do_train: true
finetuning_type: lora

### LoRA配置
lora_rank: 16
lora_alpha: 32
lora_target: all
lora_dropout: 0.1

### DPO特定参数
pref_beta: 0.1  # DPO温度参数，控制偏好强度
pref_loss: sigmoid  # 损失函数：sigmoid(dpo), orpo, simpo
pref_ftx: 1e-6  # SFT损失权重

### 数据集配置
dataset: dpo_en_demo  # DPO格式数据集
template: qwen
cutoff_len: 2048
max_samples: 5000
overwrite_cache: true
preprocessing_num_workers: 16

### 训练参数 (DPO通常需要较小学习率)
per_device_train_batch_size: 1  # DPO显存需求较大
gradient_accumulation_steps: 16
learning_rate: 5e-6  # 比SFT更小的学习率
num_train_epochs: 3.0
lr_scheduler_type: cosine
warmup_ratio: 0.1
bf16: true

### 正则化
weight_decay: 0.01
max_grad_norm: 1.0

### 输出配置
output_dir: saves/qwen2.5-7b/dpo
logging_steps: 10
save_steps: 500
save_total_limit: 3
plot_loss: true
overwrite_output_dir: true

### 评估配置
evaluation_strategy: steps
eval_steps: 500
per_device_eval_batch_size: 2

### 其他配置
report_to: none
ddp_timeout: 180000000
dataloader_num_workers: 4

### DPO数据格式说明
# DPO需要偏好对数据，格式如下：
# {
#   "instruction": "请解释量子计算的基本原理",
#   "input": "",
#   "chosen": "量子计算是基于量子力学原理的计算方式...(高质量回答)",
#   "rejected": "量子计算就是很快的计算...(低质量回答)"
# }

### 训练流程建议
# 1. 首先完成SFT训练
# 2. 准备高质量的偏好对数据
# 3. 使用SFT模型作为起点进行DPO训练
# 4. 监控chosen和rejected的奖励差异
