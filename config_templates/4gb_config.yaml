# 4GB显存优化配置
# 适用于：消费级GPU (RTX 3060, GTX 1660等)
# 特点：极致显存优化，牺牲部分训练速度

### 模型配置
model_name_or_path: Qwen/Qwen2.5-7B-Instruct
quantization_bit: 4  # 4位量化
quantization_method: bnb
double_quantization: true
quantization_type: nf4
trust_remote_code: true

### 训练方法
stage: sft
do_train: true
finetuning_type: lora

### LoRA配置 (轻量级)
lora_rank: 8  # 较小的rank
lora_alpha: 16
lora_target: q_proj,v_proj  # 只训练QV投影
lora_dropout: 0.1

### 数据集配置
dataset: alpaca_en_demo
template: qwen
cutoff_len: 512  # 较短的序列长度
max_samples: 1000  # 限制样本数
overwrite_cache: true
preprocessing_num_workers: 4

### 训练参数 (显存优化)
per_device_train_batch_size: 1  # 最小批次
gradient_accumulation_steps: 32  # 大梯度累积
learning_rate: 2e-4  # 稍高的学习率补偿小批次
num_train_epochs: 5.0  # 更多轮数
lr_scheduler_type: cosine
warmup_ratio: 0.05
gradient_checkpointing: true  # 启用梯度检查点

### 内存优化
dataloader_pin_memory: false
dataloader_num_workers: 0  # 减少工作进程

### 输出配置
output_dir: saves/4gb_training
logging_steps: 5
save_steps: 200
save_total_limit: 2
plot_loss: true
overwrite_output_dir: true
save_only_model: true  # 只保存模型权重

### 其他优化
report_to: none
ddp_timeout: 180000000
