# 📁 LLaMA-Factory 配置文件模板集合

本目录包含了各种场景下的配置文件模板，帮助您快速开始训练。

## 📋 模板列表

### 🎯 基础训练模板
- `basic_sft.yaml` - 基础监督微调
- `basic_qlora.yaml` - 量化LoRA训练
- `basic_full.yaml` - 全参数微调

### 💬 对话模型模板
- `chat_qwen.yaml` - Qwen对话模型
- `chat_llama.yaml` - LLaMA对话模型
- `chat_chatglm.yaml` - ChatGLM对话模型

### 🔧 专业任务模板
- `medical_qa.yaml` - 医疗问答
- `code_assistant.yaml` - 代码助手
- `translation.yaml` - 翻译任务
- `summarization.yaml` - 文本摘要

### 🚀 高级训练模板
- `dpo_training.yaml` - DPO偏好优化
- `ppo_training.yaml` - PPO强化学习
- `multimodal.yaml` - 多模态训练

### 💾 显存优化模板
- `4gb_config.yaml` - 4GB显存配置
- `8gb_config.yaml` - 8GB显存配置
- `16gb_config.yaml` - 16GB显存配置
- `24gb_config.yaml` - 24GB+显存配置

## 🚀 使用方法

```bash
# 选择合适的模板
cp config_templates/basic_sft.yaml my_config.yaml

# 修改配置
vim my_config.yaml

# 开始训练
llamafactory-cli train my_config.yaml
```

## 📝 自定义配置

1. **选择基础模板**：根据任务类型选择最接近的模板
2. **修改模型路径**：更新 `model_name_or_path`
3. **配置数据集**：设置 `dataset` 和相关参数
4. **调整训练参数**：根据需求修改学习率、批次大小等
5. **设置输出路径**：指定 `output_dir`

## 🔍 配置文件结构

```yaml
### 模型配置
model_name_or_path: 模型路径
trust_remote_code: true/false

### 训练方法
stage: sft/dpo/ppo/rm
do_train: true
finetuning_type: lora/full

### LoRA配置 (如果使用)
lora_rank: 8/16/32/64
lora_alpha: 16/32/64/128
lora_target: all

### 数据集配置
dataset: 数据集名称
template: 模板名称
cutoff_len: 序列长度

### 训练参数
learning_rate: 学习率
num_train_epochs: 训练轮数
per_device_train_batch_size: 批次大小

### 输出配置
output_dir: 输出目录
logging_steps: 日志间隔
save_steps: 保存间隔
```

## 💡 选择建议

### 根据任务选择
- **对话任务** → `chat_*.yaml`
- **代码生成** → `code_assistant.yaml`
- **文本分类** → `basic_sft.yaml`
- **偏好优化** → `dpo_training.yaml`

### 根据显存选择
- **4GB以下** → `4gb_config.yaml`
- **8GB** → `8gb_config.yaml`
- **16GB** → `16gb_config.yaml`
- **24GB+** → `24gb_config.yaml`

### 根据模型选择
- **Qwen系列** → `chat_qwen.yaml`
- **LLaMA系列** → `chat_llama.yaml`
- **ChatGLM系列** → `chat_chatglm.yaml`

## 🛠️ 配置验证

使用以下脚本验证配置文件：

```python
# validate_config.py
import yaml
import sys

def validate_config(config_file):
    try:
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)
        
        # 检查必需字段
        required_fields = ['model_name_or_path', 'stage', 'do_train']
        for field in required_fields:
            if field not in config:
                print(f"❌ 缺少必需字段: {field}")
                return False
        
        # 检查LoRA配置
        if config.get('finetuning_type') == 'lora':
            lora_fields = ['lora_rank', 'lora_target']
            for field in lora_fields:
                if field not in config:
                    print(f"⚠️  建议添加LoRA字段: {field}")
        
        print("✅ 配置文件验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件错误: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python validate_config.py config.yaml")
        sys.exit(1)
    
    validate_config(sys.argv[1])
```

使用方法：
```bash
python validate_config.py my_config.yaml
```
