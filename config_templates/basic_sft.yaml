# 基础监督微调配置模板
# 适用于：一般的指令微调任务
# 显存需求：8-16GB

### 模型配置
model_name_or_path: Qwen/Qwen2.5-7B-Instruct  # 修改为您的模型路径
trust_remote_code: true

### 训练方法
stage: sft
do_train: true
finetuning_type: lora

### LoRA配置
lora_rank: 16
lora_alpha: 32
lora_target: all
lora_dropout: 0.1

### 数据集配置
dataset: alpaca_en_demo  # 修改为您的数据集名称
template: qwen  # 根据模型选择：qwen, llama3, chatglm3, baichuan2
cutoff_len: 2048
max_samples: 10000  # 限制样本数，设为null使用全部数据
overwrite_cache: true
preprocessing_num_workers: 16

### 训练参数
per_device_train_batch_size: 2
gradient_accumulation_steps: 8
learning_rate: 1e-4
num_train_epochs: 3.0
lr_scheduler_type: cosine
warmup_ratio: 0.1
bf16: true

### 正则化
weight_decay: 0.01
max_grad_norm: 1.0

### 输出配置
output_dir: saves/basic_sft  # 修改为您的输出目录
logging_steps: 10
save_steps: 500
save_total_limit: 3
plot_loss: true
overwrite_output_dir: true
save_only_model: false

### 评估配置
evaluation_strategy: steps
eval_steps: 500
per_device_eval_batch_size: 4

### 其他配置
report_to: none  # 可选：wandb, tensorboard, swanlab
ddp_timeout: 180000000
dataloader_num_workers: 4
