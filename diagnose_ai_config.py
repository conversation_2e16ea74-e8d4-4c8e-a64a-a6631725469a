#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI模型配置诊断和修复工具
帮助用户诊断和解决AI模型连接问题
"""

import sys
import os
import json

# 添加项目路径
sys.path.append('src')

def load_current_config():
    """加载当前配置"""
    config_file = "simple_assistant_config.json"
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 配置文件读取失败: {e}")
            return None
    else:
        print("⚠️ 配置文件不存在")
        return None

def diagnose_config(config):
    """诊断配置问题"""
    print("🔍 开始诊断AI模型配置...")
    print("=" * 50)
    
    issues = []
    suggestions = []
    
    # 检查基本配置
    provider = config.get("selected_provider", "")
    model = config.get("selected_model", "")
    api_keys = config.get("api_keys", {})
    custom_api_base = config.get("custom_api_base", "")
    
    print(f"📊 当前配置:")
    print(f"   服务商: {provider}")
    print(f"   模型: {model}")
    print(f"   自定义端点: {custom_api_base or '使用默认'}")
    print(f"   已保存的API密钥: {list(api_keys.keys())}")
    
    # 问题1: 服务商和API密钥不匹配
    if provider not in api_keys:
        issues.append(f"❌ 服务商'{provider}'没有对应的API密钥")
        suggestions.append(f"💡 请为'{provider}'配置API密钥，或切换到已有密钥的服务商")
    
    # 问题2: API密钥格式检查
    if provider in api_keys:
        api_key = api_keys[provider]
        if provider == "OpenAI" and not api_key.startswith("sk-"):
            issues.append("❌ OpenAI API密钥格式错误，应以'sk-'开头")
            suggestions.append("💡 请检查OpenAI API密钥格式")
        elif provider == "Claude" and not api_key.startswith("sk-ant-"):
            issues.append("❌ Claude API密钥格式错误，应以'sk-ant-'开头")
            suggestions.append("💡 请检查Claude API密钥格式")
        elif provider == "智谱AI" and "." not in api_key:
            issues.append("❌ 智谱AI API密钥格式错误，应包含'.'分隔符")
            suggestions.append("💡 请检查智谱AI API密钥格式")
    
    # 问题3: 模型名称检查
    if not model and not config.get("custom_model_name", ""):
        issues.append("❌ 没有选择模型或自定义模型名称")
        suggestions.append("💡 请选择预设模型或输入自定义模型名称")
    
    # 问题4: 自定义API端点检查
    if custom_api_base:
        if not (custom_api_base.startswith("http://") or custom_api_base.startswith("https://")):
            issues.append("❌ 自定义API端点格式错误")
            suggestions.append("💡 API端点应以http://或https://开头")
    
    print(f"\n🔍 诊断结果:")
    if issues:
        print("发现以下问题:")
        for issue in issues:
            print(f"   {issue}")
        print("\n💡 建议解决方案:")
        for suggestion in suggestions:
            print(f"   {suggestion}")
    else:
        print("✅ 配置看起来正常")
    
    return len(issues) == 0, issues, suggestions

def test_api_connection(config):
    """测试API连接"""
    print("\n🔗 测试API连接...")
    
    try:
        from llamafactory.webui.components.simple_assistant import call_ai_api
        
        test_messages = [
            {"role": "system", "content": "你是一个AI助手，请简短回复。"},
            {"role": "user", "content": "请回复'连接测试成功'"}
        ]
        
        print("   发送测试请求...")
        response = call_ai_api(test_messages, config)
        
        print(f"   API响应: {response[:100]}...")
        
        if "❌" in response:
            print("❌ 连接测试失败")
            return False, response
        elif "连接测试成功" in response or len(response.strip()) > 0:
            print("✅ 连接测试成功")
            return True, response
        else:
            print("⚠️ 连接可能有问题")
            return False, "收到空响应"
            
    except Exception as e:
        error_msg = f"连接测试异常: {str(e)}"
        print(f"❌ {error_msg}")
        return False, error_msg

def suggest_fixes(config, issues):
    """提供修复建议"""
    print("\n🛠️ 修复建议:")
    print("=" * 50)
    
    provider = config.get("selected_provider", "")
    api_keys = config.get("api_keys", {})
    
    # 建议1: 修复服务商和API密钥不匹配
    if provider not in api_keys and api_keys:
        available_providers = list(api_keys.keys())
        print(f"🔧 方案1: 切换到已有API密钥的服务商")
        print(f"   您已有以下服务商的API密钥: {', '.join(available_providers)}")
        print(f"   建议切换到: {available_providers[0]}")
        
        # 生成修复配置
        fixed_config = config.copy()
        fixed_config["selected_provider"] = available_providers[0]
        
        # 根据服务商设置合适的模型
        provider_models = {
            "OpenAI": "gpt-3.5-turbo",
            "Claude": "claude-3-5-haiku-20241022", 
            "智谱AI": "glm-4-air",
            "阿里云Qwen": "qwen-turbo",
            "Google Gemini": "gemini-pro"
        }
        
        if available_providers[0] in provider_models:
            fixed_config["selected_model"] = provider_models[available_providers[0]]
        
        print(f"   修复后配置:")
        print(f"     服务商: {fixed_config['selected_provider']}")
        print(f"     模型: {fixed_config['selected_model']}")
        
        return fixed_config
    
    # 建议2: 为当前服务商配置API密钥
    print(f"🔧 方案2: 为'{provider}'配置正确的API密钥")
    
    api_key_guides = {
        "OpenAI": {
            "format": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
            "url": "https://platform.openai.com/api-keys",
            "note": "需要以'sk-'开头"
        },
        "Claude": {
            "format": "sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
            "url": "https://console.anthropic.com/",
            "note": "需要以'sk-ant-'开头"
        },
        "智谱AI": {
            "format": "xxxxxxxxxxxxxxxxxxxxxxxx.xxxxxxxxxxxxxxxx",
            "url": "https://open.bigmodel.cn/",
            "note": "包含'.'分隔符的格式"
        },
        "Google Gemini": {
            "format": "AIzaxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
            "url": "https://makersuite.google.com/app/apikey",
            "note": "通常以'AIza'开头"
        },
        "阿里云Qwen": {
            "format": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
            "url": "https://dashscope.console.aliyun.com/",
            "note": "阿里云DashScope API密钥"
        }
    }
    
    if provider in api_key_guides:
        guide = api_key_guides[provider]
        print(f"   API密钥格式: {guide['format']}")
        print(f"   获取地址: {guide['url']}")
        print(f"   注意事项: {guide['note']}")
    
    return None

def auto_fix_config(config):
    """自动修复配置"""
    print("\n🔧 尝试自动修复配置...")
    
    provider = config.get("selected_provider", "")
    api_keys = config.get("api_keys", {})
    
    # 如果当前服务商没有API密钥，但有其他服务商的密钥
    if provider not in api_keys and api_keys:
        available_provider = list(api_keys.keys())[0]
        print(f"   自动切换服务商: {provider} → {available_provider}")
        
        fixed_config = config.copy()
        fixed_config["selected_provider"] = available_provider
        
        # 设置合适的模型
        provider_models = {
            "智谱AI": "glm-4-air",
            "OpenAI": "gpt-3.5-turbo",
            "Claude": "claude-3-5-haiku-20241022",
            "阿里云Qwen": "qwen-turbo",
            "Google Gemini": "gemini-pro"
        }
        
        if available_provider in provider_models:
            fixed_config["selected_model"] = provider_models[available_provider]
            print(f"   自动设置模型: {fixed_config['selected_model']}")
        
        # 保存修复后的配置
        try:
            with open("simple_assistant_config.json", 'w', encoding='utf-8') as f:
                json.dump(fixed_config, f, ensure_ascii=False, indent=2)
            print("✅ 配置已自动修复并保存")
            return fixed_config
        except Exception as e:
            print(f"❌ 保存配置失败: {e}")
            return None
    
    return None

def main():
    """主函数"""
    print("🤖 AI模型配置诊断工具")
    print("=" * 50)
    
    # 加载当前配置
    config = load_current_config()
    if not config:
        print("❌ 无法加载配置文件")
        return
    
    # 诊断配置
    is_healthy, issues, suggestions = diagnose_config(config)
    
    if not is_healthy:
        print(f"\n🚨 发现 {len(issues)} 个配置问题")
        
        # 尝试自动修复
        fixed_config = auto_fix_config(config)
        if fixed_config:
            print("\n🔄 使用修复后的配置重新测试...")
            config = fixed_config
        else:
            # 提供手动修复建议
            suggest_fixes(config, issues)
            print("\n⚠️ 请手动修复配置后重新测试")
            return
    
    # 测试API连接
    success, response = test_api_connection(config)
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 AI模型配置正常，可以进行对话！")
        print("💡 现在可以在Web UI中使用AI助手功能")
    else:
        print("❌ 连接仍然失败")
        print(f"错误信息: {response}")
        print("\n🛠️ 请检查以下项目:")
        print("1. API密钥是否正确")
        print("2. 网络连接是否正常") 
        print("3. API服务是否可用")
        print("4. 是否有足够的API配额")

if __name__ == "__main__":
    main()
