<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <!-- Simplified version for favicon -->
  <defs>
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E5BBA;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="crossGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6B6B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E74C3C;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="16" cy="16" r="15" fill="url(#bgGrad)" stroke="#1A365D" stroke-width="1"/>
  
  <!-- Medical cross -->
  <g transform="translate(16, 16)">
    <rect x="-1.5" y="-6" width="3" height="12" fill="url(#crossGrad)" rx="0.5"/>
    <rect x="-6" y="-1.5" width="12" height="3" fill="url(#crossGrad)" rx="0.5"/>
  </g>
  
  <!-- AI nodes (simplified) -->
  <circle cx="8" cy="12" r="1" fill="#00D4AA"/>
  <circle cx="24" cy="12" r="1" fill="#00D4AA"/>
  <circle cx="8" cy="20" r="1" fill="#00D4AA"/>
  <circle cx="24" cy="20" r="1" fill="#00D4AA"/>
  
  <!-- Connection lines -->
  <line x1="9" y1="12" x2="23" y2="12" stroke="#00D4AA" stroke-width="0.5" opacity="0.7"/>
  <line x1="9" y1="20" x2="23" y2="20" stroke="#00D4AA" stroke-width="0.5" opacity="0.7"/>
  
  <!-- Small moon -->
  <path d="M 24 6 A 2 2 0 1 1 24 10 A 1.5 1.5 0 1 0 24 6" fill="#FFD700" opacity="0.8"/>
  
  <!-- Magic sparkle -->
  <circle cx="6" cy="6" r="0.5" fill="#FFD700"/>
  <circle cx="26" cy="26" r="0.5" fill="#FFD700"/>
</svg>
