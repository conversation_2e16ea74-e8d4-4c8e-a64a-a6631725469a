<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle with gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E5BBA;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="crossGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6B6B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E74C3C;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="aiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00D4AA;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00A085;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Main background circle -->
  <circle cx="100" cy="100" r="95" fill="url(#bgGradient)" stroke="#1A365D" stroke-width="2"/>
  
  <!-- Medical cross -->
  <g transform="translate(100, 100)">
    <!-- Vertical bar of cross -->
    <rect x="-8" y="-35" width="16" height="70" fill="url(#crossGradient)" rx="3"/>
    <!-- Horizontal bar of cross -->
    <rect x="-35" y="-8" width="70" height="16" fill="url(#crossGradient)" rx="3"/>
  </g>
  
  <!-- AI/Neural network elements -->
  <g transform="translate(100, 100)" filter="url(#glow)">
    <!-- Neural nodes -->
    <circle cx="-50" cy="-30" r="4" fill="url(#aiGradient)"/>
    <circle cx="-50" cy="0" r="4" fill="url(#aiGradient)"/>
    <circle cx="-50" cy="30" r="4" fill="url(#aiGradient)"/>
    
    <circle cx="50" cy="-30" r="4" fill="url(#aiGradient)"/>
    <circle cx="50" cy="0" r="4" fill="url(#aiGradient)"/>
    <circle cx="50" cy="30" r="4" fill="url(#aiGradient)"/>
    
    <!-- Connection lines -->
    <line x1="-46" y1="-30" x2="46" y2="-30" stroke="url(#aiGradient)" stroke-width="1.5" opacity="0.7"/>
    <line x1="-46" y1="0" x2="46" y2="0" stroke="url(#aiGradient)" stroke-width="1.5" opacity="0.7"/>
    <line x1="-46" y1="30" x2="46" y2="30" stroke="url(#aiGradient)" stroke-width="1.5" opacity="0.7"/>
    
    <!-- Cross connections -->
    <line x1="-46" y1="-30" x2="46" y2="0" stroke="url(#aiGradient)" stroke-width="1" opacity="0.5"/>
    <line x1="-46" y1="0" x2="46" y2="30" stroke="url(#aiGradient)" stroke-width="1" opacity="0.5"/>
    <line x1="-46" y1="30" x2="46" y2="-30" stroke="url(#aiGradient)" stroke-width="1" opacity="0.5"/>
  </g>
  
  <!-- Sleep/moon symbol -->
  <g transform="translate(140, 60)">
    <path d="M 0 0 A 12 12 0 1 1 0 24 A 9 9 0 1 0 0 0" fill="#FFD700" opacity="0.8"/>
    <!-- Small stars -->
    <circle cx="15" cy="5" r="1" fill="#FFD700"/>
    <circle cx="20" cy="15" r="1.5" fill="#FFD700"/>
    <circle cx="12" cy="25" r="1" fill="#FFD700"/>
  </g>
  
  <!-- Magic sparkles -->
  <g fill="#FFD700" opacity="0.6">
    <circle cx="30" cy="40" r="1.5"/>
    <circle cx="170" cy="160" r="1.5"/>
    <circle cx="40" cy="160" r="1"/>
    <circle cx="160" cy="40" r="1"/>
    
    <!-- Star shapes -->
    <g transform="translate(25, 170)">
      <path d="M 0 -3 L 1 -1 L 3 0 L 1 1 L 0 3 L -1 1 L -3 0 L -1 -1 Z" fill="#FFD700"/>
    </g>
    <g transform="translate(175, 30)">
      <path d="M 0 -2 L 0.7 -0.7 L 2 0 L 0.7 0.7 L 0 2 L -0.7 0.7 L -2 0 L -0.7 -0.7 Z" fill="#FFD700"/>
    </g>
  </g>
  
  <!-- Outer ring with subtle pattern -->
  <circle cx="100" cy="100" r="95" fill="none" stroke="#1A365D" stroke-width="3" opacity="0.3"/>
  <circle cx="100" cy="100" r="88" fill="none" stroke="#FFFFFF" stroke-width="1" opacity="0.2"/>
</svg>
