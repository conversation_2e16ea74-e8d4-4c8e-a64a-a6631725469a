#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能AI助手测试脚本
测试智能助手的各项功能是否正常工作
"""

import json
import os
import sys
import time
from typing import Dict, Any

# 添加项目路径
sys.path.append('src')

try:
    from llamafactory.webui.components.smart_assistant import (
        load_config,
        save_config,
        get_hardware_recommendations,
        analyze_training_log,
        generate_config_template,
        call_ai_api,
        AI_MODELS,
        API_ENDPOINTS,
        QUICK_QUESTIONS,
        SYSTEM_PROMPT
    )
    print("✅ 智能助手模块导入成功")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    sys.exit(1)

def test_config_management():
    """测试配置管理功能"""
    print("\n🔧 测试配置管理功能...")
    
    # 测试加载默认配置
    config = load_config()
    assert isinstance(config, dict), "配置应该是字典类型"
    assert "selected_provider" in config, "配置应包含selected_provider"
    assert "selected_model" in config, "配置应包含selected_model"
    print("✅ 默认配置加载成功")
    
    # 测试保存配置
    test_config = {
        "selected_provider": "OpenAI",
        "selected_model": "gpt-4",
        "api_keys": {"OpenAI": "test-key"},
        "temperature": 0.8,
        "max_tokens": 1500
    }
    
    save_config(test_config)
    
    # 验证保存的配置
    loaded_config = load_config()
    assert loaded_config["selected_provider"] == "OpenAI", "提供商配置保存失败"
    assert loaded_config["selected_model"] == "gpt-4", "模型配置保存失败"
    assert loaded_config["temperature"] == 0.8, "温度参数保存失败"
    print("✅ 配置保存和加载功能正常")

def test_hardware_recommendations():
    """测试硬件推荐功能"""
    print("\n💾 测试硬件推荐功能...")
    
    # 测试不同显存大小的推荐
    test_cases = [
        (4, "4GB显存配置"),
        (8, "8GB显存配置"), 
        (16, "16GB显存配置"),
        (24, "24GB+显存配置")
    ]
    
    for gpu_memory, expected_desc in test_cases:
        recommendations = get_hardware_recommendations(gpu_memory)
        assert isinstance(recommendations, dict), f"{gpu_memory}GB推荐应该是字典"
        assert "recommendation" in recommendations, f"{gpu_memory}GB推荐应包含描述"
        assert expected_desc in recommendations["recommendation"], f"{gpu_memory}GB推荐描述不正确"
        print(f"✅ {gpu_memory}GB显存推荐正常: {recommendations['recommendation']}")

def test_log_analysis():
    """测试日志分析功能"""
    print("\n📊 测试日志分析功能...")
    
    # 测试显存不足日志
    oom_log = "RuntimeError: CUDA out of memory. Tried to allocate 2.00 GiB"
    analysis = analyze_training_log(oom_log)
    assert "显存不足" in analysis, "应该检测到显存不足问题"
    assert "per_device_train_batch_size" in analysis, "应该包含批次大小建议"
    print("✅ 显存不足日志分析正常")
    
    # 测试NaN值日志
    nan_log = "Training loss became nan at step 100"
    analysis = analyze_training_log(nan_log)
    assert "NaN值" in analysis, "应该检测到NaN值问题"
    assert "learning_rate" in analysis, "应该包含学习率建议"
    print("✅ NaN值日志分析正常")
    
    # 测试正常日志
    normal_log = "Training step 100, loss: 2.345"
    analysis = analyze_training_log(normal_log)
    assert "正常" in analysis, "应该识别为正常日志"
    print("✅ 正常日志分析正常")

def test_config_template_generation():
    """测试配置模板生成功能"""
    print("\n📝 测试配置模板生成功能...")
    
    # 测试对话微调模板
    template = generate_config_template("对话微调", "8GB")
    assert isinstance(template, str), "模板应该是字符串"
    assert "model_name_or_path" in template, "模板应包含模型路径"
    assert "lora_rank" in template, "模板应包含LoRA配置"
    assert "8GB" in template, "模板应包含显存标识"
    print("✅ 配置模板生成正常")

def test_model_configurations():
    """测试模型配置"""
    print("\n🤖 测试模型配置...")
    
    # 验证AI模型配置
    assert isinstance(AI_MODELS, dict), "AI_MODELS应该是字典"
    assert "OpenAI" in AI_MODELS, "应该包含OpenAI配置"
    assert "Claude" in AI_MODELS, "应该包含Claude配置"
    assert "智谱AI" in AI_MODELS, "应该包含智谱AI配置"
    print(f"✅ 支持 {len(AI_MODELS)} 个AI服务提供商")
    
    # 验证API端点配置
    assert isinstance(API_ENDPOINTS, dict), "API_ENDPOINTS应该是字典"
    for provider in AI_MODELS.keys():
        assert provider in API_ENDPOINTS, f"缺少{provider}的API端点配置"
    print("✅ API端点配置完整")
    
    # 验证快捷问题
    assert isinstance(QUICK_QUESTIONS, list), "QUICK_QUESTIONS应该是列表"
    assert len(QUICK_QUESTIONS) > 0, "应该有快捷问题"
    print(f"✅ 配置了 {len(QUICK_QUESTIONS)} 个快捷问题")

def test_system_prompt():
    """测试系统提示词"""
    print("\n💬 测试系统提示词...")
    
    assert isinstance(SYSTEM_PROMPT, str), "系统提示词应该是字符串"
    assert len(SYSTEM_PROMPT) > 100, "系统提示词应该足够详细"
    assert "LLaMA-Factory" in SYSTEM_PROMPT, "应该包含项目名称"
    assert "微调" in SYSTEM_PROMPT, "应该包含微调相关内容"
    print("✅ 系统提示词配置正常")

def test_api_call_mock():
    """测试API调用（模拟）"""
    print("\n🌐 测试API调用接口...")
    
    # 模拟配置
    mock_config = {
        "selected_provider": "OpenAI",
        "selected_model": "gpt-3.5-turbo",
        "api_keys": {},  # 空的API密钥用于测试
        "temperature": 0.7,
        "max_tokens": 1000
    }
    
    messages = [
        {"role": "system", "content": "你是一个测试助手"},
        {"role": "user", "content": "测试消息"}
    ]
    
    # 调用API（应该返回错误，因为没有API密钥）
    response = call_ai_api(messages, mock_config)
    assert isinstance(response, str), "API响应应该是字符串"
    assert "API Key" in response, "应该提示需要配置API Key"
    print("✅ API调用接口正常（正确处理缺失API Key的情况）")

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始智能AI助手功能测试...")
    print("=" * 50)
    
    try:
        test_config_management()
        test_hardware_recommendations()
        test_log_analysis()
        test_config_template_generation()
        test_model_configurations()
        test_system_prompt()
        test_api_call_mock()
        
        print("\n" + "=" * 50)
        print("🎉 所有测试通过！智能AI助手功能正常")
        print("\n📋 测试总结:")
        print("✅ 配置管理功能")
        print("✅ 硬件推荐功能")
        print("✅ 日志分析功能")
        print("✅ 配置模板生成")
        print("✅ 模型配置管理")
        print("✅ 系统提示词")
        print("✅ API调用接口")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试文件
        if os.path.exists("smart_assistant_config.json"):
            os.remove("smart_assistant_config.json")
            print("\n🧹 清理测试文件完成")

def demo_usage():
    """演示智能助手的使用"""
    print("\n🎯 智能AI助手使用演示:")
    print("-" * 30)
    
    # 演示硬件推荐
    print("\n💾 硬件推荐演示:")
    for gpu_memory in [4, 8, 16, 24]:
        rec = get_hardware_recommendations(gpu_memory)
        print(f"  {gpu_memory}GB显存: {rec['recommendation']}")
    
    # 演示日志分析
    print("\n📊 日志分析演示:")
    sample_logs = [
        "CUDA out of memory",
        "loss became nan",
        "Training step 100, loss: 1.234"
    ]
    
    for log in sample_logs:
        analysis = analyze_training_log(log)
        print(f"  日志: {log}")
        print(f"  分析: {analysis.split(chr(10))[0]}")
    
    # 演示快捷问题
    print("\n🚀 快捷问题演示:")
    for i, question in enumerate(QUICK_QUESTIONS[:3], 1):
        print(f"  {i}. {question}")

if __name__ == "__main__":
    print("🤖 LLaMA-Factory 智能AI助手测试")
    print("=" * 50)
    
    # 运行测试
    success = run_all_tests()
    
    if success:
        # 演示使用
        demo_usage()
        
        print("\n🎊 智能AI助手已准备就绪！")
        print("💡 启动Web UI后，在右下角找到'🤖 AI助手'按钮开始使用")
    else:
        print("\n🛑 请修复测试中发现的问题后再使用")
        sys.exit(1)
