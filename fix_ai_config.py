#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速修复AI配置工具
自动修复服务商与API密钥不匹配的问题
"""

import json
import os

def fix_config():
    """修复配置文件"""
    config_file = "simple_assistant_config.json"
    
    if not os.path.exists(config_file):
        print("❌ 配置文件不存在")
        return False
    
    try:
        # 读取当前配置
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("🔍 当前配置:")
        print(f"   服务商: {config.get('selected_provider', 'N/A')}")
        print(f"   模型: {config.get('selected_model', 'N/A')}")
        print(f"   自定义模型: {config.get('custom_model_name', 'N/A')}")
        print(f"   API密钥: {list(config.get('api_keys', {}).keys())}")
        
        # 检查问题
        provider = config.get('selected_provider', '')
        api_keys = config.get('api_keys', {})
        
        if provider not in api_keys:
            print(f"\n❌ 问题: 服务商'{provider}'没有对应的API密钥")
            
            if api_keys:
                # 自动切换到有API密钥的服务商
                available_provider = list(api_keys.keys())[0]
                print(f"🔧 自动修复: 切换到'{available_provider}'")
                
                # 更新配置
                config['selected_provider'] = available_provider
                
                # 设置合适的模型
                if available_provider == "智谱AI":
                    config['selected_model'] = "glm-4-air"
                    config['custom_model_name'] = ""  # 清空自定义模型
                    print("   设置模型: glm-4-air")
                elif available_provider == "OpenAI":
                    config['selected_model'] = "gpt-3.5-turbo"
                    config['custom_model_name'] = ""
                    print("   设置模型: gpt-3.5-turbo")
                elif available_provider == "Claude":
                    config['selected_model'] = "claude-3-5-haiku-20241022"
                    config['custom_model_name'] = ""
                    print("   设置模型: claude-3-5-haiku-20241022")
                
                # 保存修复后的配置
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)
                
                print("✅ 配置已修复并保存")
                
                print(f"\n🎯 修复后配置:")
                print(f"   服务商: {config['selected_provider']}")
                print(f"   模型: {config['selected_model']}")
                print(f"   自定义模型: {config.get('custom_model_name', '无')}")
                
                return True
            else:
                print("❌ 没有找到任何API密钥，请先配置API密钥")
                return False
        else:
            print("✅ 配置正常，服务商与API密钥匹配")
            return True
            
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def create_zhipu_config():
    """创建智谱AI配置"""
    config = {
        "selected_provider": "智谱AI",
        "selected_model": "glm-4-air",
        "custom_api_base": "",
        "custom_model_name": "",
        "api_keys": {
            "智谱AI": "fc0640f0e35d5eaf73e8d224a33663a5.amz5ny9KQ5IrpElc"
        },
        "temperature": 0.7,
        "max_tokens": 2000,
        "timeout": 30
    }
    
    try:
        with open("simple_assistant_config.json", 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        print("✅ 已创建智谱AI配置")
        return True
    except Exception as e:
        print(f"❌ 创建配置失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 AI配置快速修复工具")
    print("=" * 40)
    
    # 尝试修复现有配置
    if fix_config():
        print("\n🎉 配置修复完成！")
        print("\n💡 现在您可以:")
        print("1. 刷新Web UI页面")
        print("2. 点击'🔗 测试API连接'验证")
        print("3. 开始使用AI对话功能")
    else:
        print("\n🔄 创建新的智谱AI配置...")
        if create_zhipu_config():
            print("\n🎉 新配置创建完成！")
            print("请刷新Web UI页面查看")

if __name__ == "__main__":
    main()
