# 🚀 LLaMA-Factory Google Colab 运行指南

## 📋 目录
- [1. 本地项目上传到 Colab](#1-本地项目上传到-colab)
- [2. Colab 环境优势](#2-colab-环境优势)
- [3. 项目迁移步骤](#3-项目迁移步骤)
- [4. 环境配置](#4-环境配置)
- [5. Colab 专用配置](#5-colab-专用配置)
- [6. 数据管理](#6-数据管理)
- [7. 训练监控](#7-训练监控)
- [8. 注意事项](#8-注意事项)

---

## 1. 本地项目上传到 Colab

### 🎯 方法概览

将本地 LLaMA-Factory 项目迁移到 Colab 有以下几种方法：

1. **📁 压缩包上传** (推荐) - 直接上传项目压缩包
2. **☁️ Google Drive 同步** - 通过 Drive 同步项目文件
3. **🔗 GitHub 私有仓库** - 上传到私有仓库再克隆
4. **📂 文件夹逐个上传** - 适合小项目

### 🚀 方法1：压缩包上传 (推荐)

#### 步骤1：本地准备项目压缩包

在您的本地电脑上：

```bash
# 进入项目目录
cd /Users/<USER>/Desktop/LLaMA-Factory-main\ 2

# 创建压缩包 (排除不必要的文件)
zip -r llamafactory_project.zip . \
  -x "*.git*" \
  -x "*__pycache__*" \
  -x "*.pyc" \
  -x "*saves/*" \
  -x "*.venv*" \
  -x "*node_modules*"

# 或者使用 tar 格式
tar -czf llamafactory_project.tar.gz \
  --exclude='.git' \
  --exclude='__pycache__' \
  --exclude='*.pyc' \
  --exclude='saves' \
  --exclude='.venv' \
  .
```

#### 步骤2：在 Colab 中上传和解压

```python
# ===== Colab 中运行 =====

# 1. 上传压缩包
from google.colab import files
print("📁 请选择您的 LLaMA-Factory 项目压缩包...")
uploaded = files.upload()

# 2. 解压项目
import zipfile
import os

# 获取上传的文件名
zip_filename = list(uploaded.keys())[0]
print(f"📦 解压文件: {zip_filename}")

# 解压到当前目录
with zipfile.ZipFile(zip_filename, 'r') as zip_ref:
    zip_ref.extractall('/content/LLaMA-Factory')

# 切换到项目目录
os.chdir('/content/LLaMA-Factory')

# 3. 验证项目结构
print("📋 项目结构:")
!ls -la

print("\n📋 源码目录:")
!ls -la src/

print("✅ 项目上传完成！")
```

### 🔄 方法2：Google Drive 同步

#### 步骤1：将项目上传到 Google Drive

1. 在本地将整个 LLaMA-Factory 项目文件夹上传到 Google Drive
2. 确保项目在 Drive 中的路径，例如：`/MyDrive/LLaMA-Factory/`

#### 步骤2：在 Colab 中同步

```python
# ===== Colab 中运行 =====

# 1. 挂载 Google Drive
from google.colab import drive
drive.mount('/content/drive')

# 2. 复制项目到 Colab 工作目录
import shutil
import os

# 设置 Drive 中的项目路径
drive_project_path = '/content/drive/MyDrive/LLaMA-Factory'
colab_project_path = '/content/LLaMA-Factory'

# 复制整个项目
if os.path.exists(drive_project_path):
    print("📁 从 Google Drive 复制项目...")
    shutil.copytree(drive_project_path, colab_project_path)
    print("✅ 项目复制完成！")
else:
    print("❌ 在 Google Drive 中未找到项目，请检查路径")

# 3. 切换到项目目录
os.chdir(colab_project_path)

# 4. 验证项目
print("📋 项目结构:")
!ls -la
```

### 🔗 方法3：GitHub 私有仓库

如果您有 GitHub 账号，可以创建私有仓库：

```python
# ===== Colab 中运行 =====

# 1. 克隆您的私有仓库
!git clone https://github.com/YOUR_USERNAME/YOUR_PRIVATE_REPO.git LLaMA-Factory

# 2. 切换到项目目录
%cd LLaMA-Factory

# 3. 如果需要认证
from getpass import getpass
token = getpass('请输入 GitHub Personal Access Token: ')

# 配置 git 认证
!git config --global credential.helper store
!echo "https://YOUR_USERNAME:{token}@github.com" > ~/.git-credentials
```

---

## 2. Colab 环境优势

### ✅ **免费GPU资源**
- **T4 GPU**: 16GB显存，适合中小型模型
- **V100 GPU**: 16GB显存，性能更强（Pro版本）
- **A100 GPU**: 40GB显存，支持大模型（Pro+版本）

### ✅ **预装环境**
- Python 3.10
- CUDA 12.x
- PyTorch 预装
- Jupyter Notebook 界面

### ✅ **云端存储**
- Google Drive 集成
- 自动保存训练结果
- 跨设备访问

---

## 3. 项目迁移步骤

### 📋 完整迁移检查清单

#### ✅ **必需文件**
- [ ] `src/` 目录 (核心源码)
- [ ] `data/` 目录 (数据集和配置)
- [ ] `requirements.txt` 或 `pyproject.toml`
- [ ] `README.md`

#### ✅ **可选文件**
- [ ] `examples/` 目录 (示例配置)
- [ ] `docs/` 目录 (文档)
- [ ] 自定义配置文件

#### ❌ **不需要的文件**
- [ ] `.git/` 目录 (版本控制)
- [ ] `__pycache__/` 目录 (Python缓存)
- [ ] `.venv/` 目录 (虚拟环境)
- [ ] `saves/` 目录 (训练输出，太大)
- [ ] `*.pyc` 文件

### 🔍 项目验证脚本

上传项目后，运行此脚本验证项目完整性：

```python
# ===== 项目完整性验证 =====

import os
import sys

def verify_project_structure():
    """验证 LLaMA-Factory 项目结构"""

    required_files = [
        'src/llamafactory',
        'src/webui.py',
        'data/dataset_info.json',
        'pyproject.toml'
    ]

    optional_files = [
        'examples/',
        'README.md',
        'requirements.txt'
    ]

    print("🔍 验证项目结构...")

    # 检查必需文件
    missing_required = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_required.append(file_path)
        else:
            print(f"✅ {file_path}")

    # 检查可选文件
    for file_path in optional_files:
        if os.path.exists(file_path):
            print(f"📁 {file_path} (可选)")
        else:
            print(f"⚠️  {file_path} (缺失，但不影响运行)")

    # 结果
    if missing_required:
        print(f"\n❌ 缺少必需文件: {missing_required}")
        print("请重新上传完整的项目文件")
        return False
    else:
        print("\n✅ 项目结构验证通过！")
        return True

# 运行验证
if verify_project_structure():
    print("🚀 可以开始安装依赖了！")
else:
    print("🛑 请先修复项目结构问题")
```

---

## 4. 环境配置

### 4.1 本地项目依赖安装

```python
# ===== 本地项目环境配置 =====

import os
import subprocess
import sys

def install_local_project():
    """为本地上传的项目安装依赖"""

    # 检查GPU
    print("🔍 检查GPU信息...")
    !nvidia-smi

    # 确认在项目目录
    if not os.path.exists('src/llamafactory'):
        print("❌ 未找到 LLaMA-Factory 项目，请先上传项目文件")
        return False

    print("📁 当前项目目录:")
    !pwd
    !ls -la

    # 安装基础依赖
    print("📦 安装基础依赖...")

    # 方法1: 使用 pyproject.toml (推荐)
    if os.path.exists('pyproject.toml'):
        !pip install -e ".[torch,metrics]"
    # 方法2: 使用 requirements.txt
    elif os.path.exists('requirements.txt'):
        !pip install -r requirements.txt
        !pip install -e .
    else:
        print("⚠️ 未找到依赖文件，手动安装核心依赖...")
        !pip install torch torchvision torchaudio
        !pip install transformers datasets accelerate peft trl
        !pip install gradio matplotlib tensorboard
        !pip install -e .

    # 安装优化依赖
    print("⚡ 安装优化依赖...")
    try:
        !pip install flash-attn --no-build-isolation
        print("✅ Flash Attention 安装成功")
    except Exception as e:
        print(f"⚠️ Flash Attention 安装失败: {e}")

    try:
        !pip install "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git"
        print("✅ Unsloth 安装成功")
    except Exception as e:
        print(f"⚠️ Unsloth 安装失败: {e}")

    # 验证安装
    print("🔍 验证安装...")
    try:
        !llamafactory-cli version
        print("✅ LLaMA-Factory 安装成功！")
        return True
    except:
        print("❌ 安装验证失败")
        return False

# 运行安装
if install_local_project():
    print("🎉 环境配置完成，可以开始训练了！")
else:
    print("🛑 环境配置失败，请检查项目文件")
```

### 4.2 验证安装

```python
# 验证安装
!llamafactory-cli version

# 检查可用模型
!llamafactory-cli train --help | head -20

# 测试 Web UI 启动
print("🚀 启动 Web UI 测试...")
!timeout 10 python src/webui.py --host 0.0.0.0 --port 7860 || echo "Web UI 可以正常启动"
```

### 4.3 完整本地项目迁移示例

```python
# ===== 完整的本地项目迁移流程 =====

def complete_migration_workflow():
    """完整的项目迁移工作流程"""

    print("🎯 开始 LLaMA-Factory 本地项目迁移到 Colab")
    print("=" * 50)

    # 步骤1: 上传项目
    print("\n📁 步骤1: 上传项目文件")
    from google.colab import files
    import zipfile
    import os

    # 检查是否已有项目
    if os.path.exists('/content/LLaMA-Factory'):
        print("⚠️ 检测到已存在的项目，是否要重新上传？")
        choice = input("输入 'y' 重新上传，其他键跳过: ")
        if choice.lower() == 'y':
            !rm -rf /content/LLaMA-Factory
        else:
            os.chdir('/content/LLaMA-Factory')
            print("✅ 使用现有项目")
            return True

    print("请上传您的 LLaMA-Factory 项目压缩包...")
    uploaded = files.upload()

    if not uploaded:
        print("❌ 未上传文件")
        return False

    # 解压项目
    zip_filename = list(uploaded.keys())[0]
    print(f"📦 解压文件: {zip_filename}")

    try:
        with zipfile.ZipFile(zip_filename, 'r') as zip_ref:
            zip_ref.extractall('/content/LLaMA-Factory')
        os.chdir('/content/LLaMA-Factory')
        print("✅ 项目解压成功")
    except Exception as e:
        print(f"❌ 解压失败: {e}")
        return False

    # 步骤2: 验证项目结构
    print("\n🔍 步骤2: 验证项目结构")
    required_files = ['src/llamafactory', 'src/webui.py', 'data/dataset_info.json']
    missing_files = [f for f in required_files if not os.path.exists(f)]

    if missing_files:
        print(f"❌ 缺少必需文件: {missing_files}")
        return False
    else:
        print("✅ 项目结构验证通过")

    # 步骤3: 安装依赖
    print("\n📦 步骤3: 安装依赖")
    try:
        !pip install -e ".[torch,metrics]"
        print("✅ 基础依赖安装成功")
    except Exception as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

    # 步骤4: 验证安装
    print("\n🔍 步骤4: 验证安装")
    try:
        !llamafactory-cli version
        print("✅ LLaMA-Factory 安装验证成功")
    except:
        print("❌ 安装验证失败")
        return False

    # 步骤5: 挂载 Google Drive (用于保存结果)
    print("\n☁️ 步骤5: 挂载 Google Drive")
    from google.colab import drive
    drive.mount('/content/drive')

    # 创建输出目录
    !mkdir -p /content/drive/MyDrive/llamafactory_outputs
    print("✅ Google Drive 挂载成功")

    print("\n🎉 项目迁移完成！")
    print("=" * 50)
    print("📋 下一步操作:")
    print("1. 配置训练参数")
    print("2. 准备训练数据")
    print("3. 开始训练")
    print("4. 监控训练进度")

    return True

# 运行完整迁移流程
complete_migration_workflow()
```

---

## 4. Colab 专用配置

### 4.1 Colab T4 GPU 配置 (16GB显存)

```yaml
# colab_t4_config.yaml
### 模型配置
model_name_or_path: Qwen/Qwen2.5-7B-Instruct
quantization_bit: 4  # 4位量化节省显存
quantization_method: bnb
trust_remote_code: true

### 训练方法
stage: sft
do_train: true
finetuning_type: lora

### LoRA配置
lora_rank: 16
lora_alpha: 32
lora_target: all
lora_dropout: 0.1

### 数据集配置
dataset: alpaca_en_demo
template: qwen
cutoff_len: 1024  # Colab适中长度
max_samples: 2000  # 限制样本数

### 训练参数 (Colab优化)
per_device_train_batch_size: 2
gradient_accumulation_steps: 8
learning_rate: 1e-4
num_train_epochs: 3.0
lr_scheduler_type: cosine
warmup_ratio: 0.1
bf16: true
gradient_checkpointing: true

### 输出配置
output_dir: /content/drive/MyDrive/llamafactory_outputs
logging_steps: 10
save_steps: 200
save_total_limit: 2
plot_loss: true
```

### 4.2 Colab Pro/Pro+ 配置 (更大显存)

```yaml
# colab_pro_config.yaml
### 模型配置
model_name_or_path: Qwen/Qwen2.5-7B-Instruct
trust_remote_code: true

### 训练方法
stage: sft
do_train: true
finetuning_type: lora

### LoRA配置
lora_rank: 32  # 更大rank
lora_alpha: 64
lora_target: all
use_dora: true  # 使用DoRA

### 数据集配置
dataset: alpaca_en_demo,identity
template: qwen
cutoff_len: 2048  # 更长序列
max_samples: 5000

### 训练参数
per_device_train_batch_size: 4
gradient_accumulation_steps: 4
learning_rate: 5e-5
num_train_epochs: 5.0
lr_scheduler_type: cosine
warmup_ratio: 0.1
bf16: true

### 输出配置
output_dir: /content/drive/MyDrive/llamafactory_outputs
logging_steps: 10
save_steps: 500
```

---

## 5. 数据管理

### 5.1 挂载 Google Drive

```python
# 挂载 Google Drive
from google.colab import drive
drive.mount('/content/drive')

# 创建工作目录
!mkdir -p /content/drive/MyDrive/llamafactory_data
!mkdir -p /content/drive/MyDrive/llamafactory_outputs
```

### 5.2 上传自定义数据

```python
# 方法1: 从 Google Drive 加载数据
import shutil

# 复制数据到工作目录
shutil.copy('/content/drive/MyDrive/my_dataset.json', 
           '/content/LLaMA-Factory/data/')

# 方法2: 直接上传文件
from google.colab import files
uploaded = files.upload()

# 移动到数据目录
for filename in uploaded.keys():
    shutil.move(filename, f'/content/LLaMA-Factory/data/{filename}')
```

### 5.3 配置数据集

```python
# 更新 dataset_info.json
import json

dataset_info = {
    "my_colab_dataset": {
        "file_name": "my_dataset.json",
        "formatting": "alpaca"
    }
}

# 读取现有配置
with open('/content/LLaMA-Factory/data/dataset_info.json', 'r') as f:
    existing_info = json.load(f)

# 添加新数据集
existing_info.update(dataset_info)

# 保存配置
with open('/content/LLaMA-Factory/data/dataset_info.json', 'w') as f:
    json.dump(existing_info, f, indent=2)

print("✅ 数据集配置已更新")
```

---

## 6. 训练监控

### 6.1 Colab 训练脚本

```python
# ===== Colab 训练脚本 =====

import subprocess
import threading
import time
import json
from IPython.display import clear_output
import matplotlib.pyplot as plt

def run_training_with_monitoring():
    """运行训练并实时监控"""
    
    # 训练命令
    cmd = [
        "llamafactory-cli", "train",
        "--model_name_or_path", "Qwen/Qwen2.5-7B-Instruct",
        "--stage", "sft",
        "--do_train",
        "--finetuning_type", "lora",
        "--lora_rank", "16",
        "--dataset", "alpaca_en_demo",
        "--template", "qwen",
        "--cutoff_len", "1024",
        "--learning_rate", "1e-4",
        "--num_train_epochs", "3.0",
        "--output_dir", "/content/drive/MyDrive/llamafactory_outputs",
        "--per_device_train_batch_size", "2",
        "--gradient_accumulation_steps", "8",
        "--save_steps", "200",
        "--logging_steps", "10",
        "--plot_loss",
        "--bf16",
        "--quantization_bit", "4"
    ]
    
    # 启动训练进程
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, 
                              stderr=subprocess.STDOUT, 
                              universal_newlines=True)
    
    # 监控输出
    for line in process.stdout:
        print(line.strip())
        
        # 检查是否完成
        if "Training completed" in line:
            break

# 运行训练
run_training_with_monitoring()
```

### 6.2 实时损失监控

```python
# 实时监控训练损失
import matplotlib.pyplot as plt
import json
import time
from IPython.display import clear_output

def monitor_training_loss(log_file="/content/drive/MyDrive/llamafactory_outputs/trainer_log.jsonl"):
    """实时监控训练损失"""
    
    plt.figure(figsize=(10, 6))
    
    while True:
        try:
            # 读取日志
            logs = []
            with open(log_file, 'r') as f:
                for line in f:
                    try:
                        logs.append(json.loads(line))
                    except:
                        continue
            
            if logs:
                # 提取训练数据
                train_logs = [log for log in logs if 'train_loss' in log]
                if len(train_logs) > 1:
                    steps = [log['step'] for log in train_logs]
                    losses = [log['train_loss'] for log in train_logs]
                    
                    # 清除输出并重新绘制
                    clear_output(wait=True)
                    
                    plt.clf()
                    plt.plot(steps, losses, 'b-', linewidth=2, marker='o', markersize=4)
                    plt.title(f'训练损失曲线 (当前损失: {losses[-1]:.4f})', fontsize=14)
                    plt.xlabel('训练步数', fontsize=12)
                    plt.ylabel('损失值', fontsize=12)
                    plt.grid(True, alpha=0.3)
                    plt.tight_layout()
                    plt.show()
                    
                    print(f"📊 步数: {steps[-1]}, 损失: {losses[-1]:.4f}")
                    print(f"📈 总共训练了 {len(train_logs)} 步")
            
            time.sleep(30)  # 每30秒更新一次
            
        except FileNotFoundError:
            print("⏳ 等待训练日志文件生成...")
            time.sleep(10)
        except KeyboardInterrupt:
            print("🛑 监控已停止")
            break

# 在另一个cell中运行监控
# monitor_training_loss()
```

---

## 7. 注意事项

### 7.1 Colab 限制

#### ⏰ **运行时限制**
- **免费版**: 最长12小时连续运行
- **Pro版**: 最长24小时连续运行
- **解决方案**: 
  ```python
  # 定期保存检查点
  save_steps: 200  # 更频繁保存
  
  # 自动恢复训练
  resume_from_checkpoint: "latest"
  ```

#### 💾 **存储限制**
- **临时存储**: 会话结束后丢失
- **解决方案**: 保存到 Google Drive
  ```yaml
  output_dir: /content/drive/MyDrive/llamafactory_outputs
  ```

#### 🔄 **会话重置**
- **现象**: 长时间不活动会重置
- **解决方案**: 
  ```python
  # 保持活跃的脚本
  import time
  import random
  
  def keep_alive():
      while True:
          time.sleep(random.randint(300, 600))  # 5-10分钟
          print("🔄 保持会话活跃...")
  
  # 在后台运行
  import threading
  threading.Thread(target=keep_alive, daemon=True).start()
  ```

### 7.2 优化建议

#### 🚀 **性能优化**
```python
# 1. 使用量化减少显存
quantization_bit: 4

# 2. 启用梯度检查点
gradient_checkpointing: true

# 3. 优化批次大小
per_device_train_batch_size: 2
gradient_accumulation_steps: 8

# 4. 使用混合精度
bf16: true
```

#### 📊 **监控优化**
```python
# 1. 更频繁的日志
logging_steps: 10

# 2. 更频繁的保存
save_steps: 200

# 3. 限制保存数量
save_total_limit: 2
```

### 7.3 完整 Colab Notebook 模板

```python
# ===== LLaMA-Factory Colab 完整模板 =====

# 1. 环境检查
print("🔍 检查环境...")
!nvidia-smi
!python --version

# 2. 挂载 Google Drive
from google.colab import drive
drive.mount('/content/drive')

# 3. 安装 LLaMA-Factory
print("📦 安装 LLaMA-Factory...")
!git clone https://github.com/hiyouga/LLaMA-Factory.git
%cd LLaMA-Factory
!pip install -e ".[torch,metrics]"

# 4. 创建配置文件
config = """
model_name_or_path: Qwen/Qwen2.5-7B-Instruct
quantization_bit: 4
quantization_method: bnb
trust_remote_code: true
stage: sft
do_train: true
finetuning_type: lora
lora_rank: 16
lora_alpha: 32
lora_target: all
dataset: alpaca_en_demo
template: qwen
cutoff_len: 1024
max_samples: 2000
per_device_train_batch_size: 2
gradient_accumulation_steps: 8
learning_rate: 1e-4
num_train_epochs: 3.0
lr_scheduler_type: cosine
warmup_ratio: 0.1
bf16: true
gradient_checkpointing: true
output_dir: /content/drive/MyDrive/llamafactory_outputs
logging_steps: 10
save_steps: 200
save_total_limit: 2
plot_loss: true
"""

with open('colab_config.yaml', 'w') as f:
    f.write(config)

# 5. 开始训练
print("🚀 开始训练...")
!llamafactory-cli train colab_config.yaml

# 6. 训练完成后测试
print("🎯 启动对话测试...")
!llamafactory-cli webchat \
    --model_name_or_path Qwen/Qwen2.5-7B-Instruct \
    --adapter_name_or_path /content/drive/MyDrive/llamafactory_outputs \
    --template qwen \
    --share
```

---

## 🎉 总结

LLaMA-Factory 在 Colab 上运行具有以下优势：

✅ **免费GPU资源** - T4/V100/A100 GPU支持  
✅ **零配置环境** - 预装所需依赖  
✅ **云端存储** - Google Drive 自动同步  
✅ **Web界面** - 直观的训练和测试界面  
✅ **分享功能** - 生成公开链接供他人访问  

现在您可以在 Colab 上轻松开始您的模型微调之旅！🚀

---

## 🎯 本地项目完整迁移示例

### 📝 完整的 Colab Notebook 模板

以下是一个完整的 Colab Notebook，专门用于运行本地的 LLaMA-Factory 项目：

```python
# ===== 本地 LLaMA-Factory 项目 Colab 运行模板 =====

# 第一步：环境检查
print("🔍 检查 Colab 环境...")
!nvidia-smi
!python --version
print("✅ 环境检查完成")

# 第二步：挂载 Google Drive
print("\n☁️ 挂载 Google Drive...")
from google.colab import drive
drive.mount('/content/drive')

# 第三步：上传本地项目
print("\n📁 上传本地 LLaMA-Factory 项目...")
from google.colab import files
import zipfile
import os

# 上传项目压缩包
print("请选择您的 LLaMA-Factory 项目压缩包 (zip格式):")
uploaded = files.upload()

# 解压项目
if uploaded:
    zip_filename = list(uploaded.keys())[0]
    print(f"📦 解压 {zip_filename}...")

    with zipfile.ZipFile(zip_filename, 'r') as zip_ref:
        zip_ref.extractall('/content/LLaMA-Factory')

    # 切换到项目目录
    os.chdir('/content/LLaMA-Factory')
    print("✅ 项目解压完成")

    # 显示项目结构
    print("\n📋 项目结构:")
    !ls -la
else:
    print("❌ 未上传文件")

# 第四步：安装依赖
print("\n📦 安装项目依赖...")

# 检查依赖文件
if os.path.exists('pyproject.toml'):
    print("使用 pyproject.toml 安装依赖...")
    !pip install -e ".[torch,metrics]"
elif os.path.exists('requirements.txt'):
    print("使用 requirements.txt 安装依赖...")
    !pip install -r requirements.txt
    !pip install -e .
else:
    print("手动安装核心依赖...")
    !pip install torch transformers datasets accelerate peft trl gradio
    !pip install -e .

# 安装优化依赖
print("安装优化依赖...")
!pip install flash-attn --no-build-isolation

print("✅ 依赖安装完成")

# 第五步：验证安装
print("\n🔍 验证安装...")
!llamafactory-cli version

# 第六步：准备训练配置
print("\n⚙️ 创建 Colab 训练配置...")

colab_config = """
### 模型配置
model_name_or_path: Qwen/Qwen2.5-7B-Instruct
quantization_bit: 4
quantization_method: bnb
trust_remote_code: true

### 训练方法
stage: sft
do_train: true
finetuning_type: lora

### LoRA配置
lora_rank: 16
lora_alpha: 32
lora_target: all
lora_dropout: 0.1

### 数据集配置
dataset: alpaca_en_demo
template: qwen
cutoff_len: 1024
max_samples: 2000

### 训练参数
per_device_train_batch_size: 2
gradient_accumulation_steps: 8
learning_rate: 1e-4
num_train_epochs: 3.0
lr_scheduler_type: cosine
warmup_ratio: 0.1
bf16: true
gradient_checkpointing: true

### 输出配置
output_dir: /content/drive/MyDrive/llamafactory_outputs
logging_steps: 10
save_steps: 200
save_total_limit: 2
plot_loss: true
overwrite_output_dir: true
"""

# 保存配置文件
with open('colab_config.yaml', 'w') as f:
    f.write(colab_config)

print("✅ 配置文件已创建: colab_config.yaml")

# 第七步：开始训练
print("\n🚀 开始训练...")
print("配置文件内容:")
!cat colab_config.yaml

# 启动训练
!llamafactory-cli train colab_config.yaml

print("🎉 训练完成！")

# 第八步：启动 Web UI 测试
print("\n🌐 启动 Web UI 进行测试...")
!python src/webui.py --host 0.0.0.0 --port 7860 --share
```

### 💡 使用提示

1. **准备工作**：
   - 在本地将 LLaMA-Factory 项目打包成 zip 文件
   - 排除不必要的文件（.git, __pycache__, saves 等）

2. **上传建议**：
   - 压缩包大小建议控制在 100MB 以内
   - 如果项目太大，考虑只上传核心文件

3. **数据管理**：
   - 训练结果自动保存到 Google Drive
   - 可以在 Drive 中查看和下载模型

4. **会话管理**：
   - Colab 免费版有12小时限制
   - 建议设置较频繁的保存间隔

### 🔧 故障排除

**问题1：上传失败**
```python
# 如果直接上传失败，可以先上传到 Drive
# 然后从 Drive 复制到 Colab
!cp -r /content/drive/MyDrive/LLaMA-Factory /content/
```

**问题2：依赖安装失败**
```python
# 手动安装核心依赖
!pip install torch==2.0.1 transformers==4.35.0
!pip install datasets accelerate peft trl
!pip install gradio matplotlib
```

**问题3：显存不足**
```python
# 使用更激进的量化设置
quantization_bit: 4
per_device_train_batch_size: 1
gradient_accumulation_steps: 16
```

现在您可以将本地的 LLaMA-Factory 项目完美迁移到 Colab 运行了！🎉
