#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试AI助手显示问题
"""

import sys
import os

# 添加项目路径
sys.path.append('src')

def check_interface_structure():
    """检查界面结构"""
    print("🔍 检查界面结构...")
    
    try:
        from llamafactory.webui.interface import create_ui
        
        # 创建UI实例
        demo = create_ui(demo_mode=False)
        
        print(f"✅ UI创建成功")
        print(f"📊 Demo类型: {type(demo)}")
        
        # 检查是否有AI助手相关的组件
        if hasattr(demo, 'blocks'):
            print(f"📋 总组件数: {len(demo.blocks) if demo.blocks else 0}")
        
        return True
        
    except Exception as e:
        print(f"❌ UI创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_assistant_import():
    """检查AI助手导入"""
    print("\n🔍 检查AI助手导入...")
    
    try:
        from llamafactory.webui.components import create_smart_assistant
        
        print("✅ create_smart_assistant 导入成功")
        
        # 尝试创建助手
        assistant = create_smart_assistant()
        print(f"📊 助手组件数: {len(assistant)}")
        print(f"📋 组件键: {list(assistant.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI助手导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_simple_assistant():
    """检查简化版AI助手"""
    print("\n🔍 检查简化版AI助手...")
    
    try:
        from llamafactory.webui.components.simple_assistant import create_simple_assistant
        
        print("✅ create_simple_assistant 导入成功")
        
        # 尝试创建助手
        assistant = create_simple_assistant()
        print(f"📊 助手组件数: {len(assistant)}")
        print(f"📋 组件键: {list(assistant.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ 简化版AI助手创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_gradio_version():
    """检查Gradio版本"""
    print("\n🔍 检查Gradio版本...")
    
    try:
        import gradio as gr
        print(f"📦 Gradio版本: {gr.__version__}")
        
        # 测试基本组件
        with gr.Blocks() as test_demo:
            gr.HTML("<h1>测试</h1>")
            with gr.Tab("测试标签"):
                gr.Textbox("测试")
        
        print("✅ Gradio基本功能正常")
        return True
        
    except Exception as e:
        print(f"❌ Gradio检查失败: {e}")
        return False

def create_minimal_assistant():
    """创建最小化AI助手测试"""
    print("\n🔧 创建最小化AI助手测试...")
    
    try:
        import gradio as gr
        
        def minimal_assistant():
            with gr.Blocks() as demo:
                gr.HTML("<h1>🤖 最小化AI助手测试</h1>")
                
                with gr.Tab("💬 对话"):
                    chatbot = gr.Chatbot(height=300)
                    msg = gr.Textbox(placeholder="输入消息...")
                    btn = gr.Button("发送")
                    
                    def chat(message, history):
                        history.append([message, f"收到: {message}"])
                        return history, ""
                    
                    btn.click(chat, [msg, chatbot], [chatbot, msg])
                
                with gr.Tab("🛠️ 工具"):
                    gr.HTML("<h3>硬件推荐</h3>")
                    gpu_input = gr.Number(label="GPU显存(GB)", value=8)
                    rec_btn = gr.Button("获取推荐")
                    rec_output = gr.Markdown()
                    
                    def get_rec(gpu_mem):
                        return f"## 推荐配置\n\n{gpu_mem}GB显存的推荐配置..."
                    
                    rec_btn.click(get_rec, gpu_input, rec_output)
            
            return demo
        
        # 创建并启动测试
        test_demo = minimal_assistant()
        print("✅ 最小化AI助手创建成功")
        
        # 启动测试服务器
        print("🚀 启动测试服务器在端口7861...")
        test_demo.launch(server_port=7861, share=False, inbrowser=False)
        
        return True
        
    except Exception as e:
        print(f"❌ 最小化AI助手创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 AI助手显示问题调试")
    print("=" * 50)
    
    # 运行各项检查
    checks = [
        check_gradio_version,
        check_assistant_import,
        check_simple_assistant,
        check_interface_structure,
    ]
    
    results = []
    for check in checks:
        try:
            result = check()
            results.append(result)
        except Exception as e:
            print(f"❌ 检查异常: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print(f"🎯 检查结果: {sum(results)}/{len(results)} 通过")
    
    if all(results):
        print("✅ 所有检查通过，AI助手应该可见")
        print("💡 请检查浏览器中的'🤖 AI助手'标签页")
    else:
        print("⚠️ 发现问题，尝试创建测试版本...")
        create_minimal_assistant()
    
    print("\n📋 故障排除建议:")
    print("1. 刷新浏览器页面 (Ctrl+F5)")
    print("2. 检查浏览器控制台是否有错误")
    print("3. 确认访问 http://127.0.0.1:7860")
    print("4. 查看是否有'🤖 AI助手'标签页")

if __name__ == "__main__":
    main()
