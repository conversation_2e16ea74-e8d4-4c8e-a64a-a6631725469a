# 📚 LLaMA-Factory 文档索引

## 🎯 文档概览

本文档集合为LLaMA-Factory用户提供了从入门到精通的完整指南，涵盖了环境配置、参数调优、实战案例等各个方面。

---

## 📖 文档列表

### 1. 🚀 快速入门
**文件**: `LLaMA-Factory_Quick_Start.md`
**适用人群**: 初学者
**内容**:
- 5分钟快速上手
- 常用配置模板
- 一键训练脚本
- 实时监控工具
- 常见问题解决

### 2. 📘 完整用户指南
**文件**: `LLaMA-Factory_Complete_User_Guide.md`
**适用人群**: 所有用户
**内容**:
- 项目概述与特性
- 环境配置与安装
- Web UI详细使用
- 模型微调参数详解
- 数据准备与格式化
- 操作流程标准化
- 故障排除指南
- 高级功能详解
- 实战案例分析

### 3. 🔧 参数详解手册
**文件**: `LLaMA-Factory_Parameters_Reference.md`
**适用人群**: 进阶用户
**内容**:
- 模型参数详解
- 训练参数说明
- LoRA参数配置
- 数据参数设置
- 优化器参数
- 量化参数
- 推理参数
- 高级参数

### 4. 📁 配置文件模板
**目录**: `config_templates/`
**适用人群**: 所有用户
**内容**:
- 基础训练模板
- 显存优化配置
- 专业任务模板
- 高级训练配置

### 5. ☁️ Google Colab 运行指南
**文件**: `LLaMA-Factory_Colab_Guide.md`
**适用人群**: 云端用户
**内容**:
- Colab 环境优势
- 一键运行脚本
- 显存优化配置
- 数据管理方案
- 实时训练监控
- 注意事项和限制

---

## 🎯 使用建议

### 新手用户路径
1. **开始** → `LLaMA-Factory_Quick_Start.md`
2. **云端** → `LLaMA-Factory_Colab_Guide.md` (推荐Colab用户)
3. **深入** → `LLaMA-Factory_Complete_User_Guide.md`
4. **实践** → `config_templates/basic_sft.yaml`

### 进阶用户路径
1. **参考** → `LLaMA-Factory_Parameters_Reference.md`
2. **优化** → 显存优化配置模板
3. **专业** → 专业任务配置模板

### 问题解决路径
1. **快速解决** → `LLaMA-Factory_Quick_Start.md` 常见问题部分
2. **详细排查** → `LLaMA-Factory_Complete_User_Guide.md` 故障排除章节
3. **参数调优** → `LLaMA-Factory_Parameters_Reference.md`

---

## 📊 配置选择指南

### 根据显存选择配置

| 显存大小 | 推荐配置 | 特点 |
|----------|----------|------|
| **4GB以下** | `config_templates/4gb_config.yaml` | 极致优化，量化训练 |
| **8GB** | `config_templates/basic_sft.yaml` + 量化 | 平衡配置 |
| **16GB** | `config_templates/basic_sft.yaml` | 标准配置 |
| **24GB+** | 高质量配置 | 全功能训练 |

### 根据任务选择配置

| 任务类型 | 推荐配置 | 说明 |
|----------|----------|------|
| **通用对话** | `config_templates/basic_sft.yaml` | 基础指令微调 |
| **医疗问答** | `config_templates/medical_qa.yaml` | 专业领域优化 |
| **代码生成** | 代码助手模板 | 长序列支持 |
| **偏好对齐** | `config_templates/dpo_training.yaml` | DPO训练 |

### 根据模型选择模板

| 模型系列 | 模板选择 | 注意事项 |
|----------|----------|----------|
| **Qwen系列** | `template: qwen` | 推荐使用 |
| **LLaMA系列** | `template: llama3` | 注意版本 |
| **ChatGLM系列** | `template: chatglm3` | 特殊格式 |
| **Baichuan系列** | `template: baichuan2` | 中文优化 |

---

## 🛠️ 工具与脚本

### 训练监控
- **实时监控**: `monitor.py` (在快速入门指南中)
- **损失可视化**: 内置plot_loss功能
- **TensorBoard**: 设置 `report_to: tensorboard`

### 配置验证
- **验证脚本**: `validate_config.py` (在配置模板目录中)
- **参数检查**: 自动检查必需字段

### 批量训练
- **自动配置**: `auto_train.sh` (根据显存自动选择)
- **批量实验**: `batch_experiment.sh` (多参数组合)

---

## 📈 学习路径

### 阶段1：基础入门 (1-2天)
- [ ] 阅读快速入门指南
- [ ] 完成第一次训练
- [ ] 熟悉Web界面
- [ ] 测试训练结果

### 阶段2：深入理解 (3-5天)
- [ ] 阅读完整用户指南
- [ ] 理解参数含义
- [ ] 尝试不同配置
- [ ] 解决常见问题

### 阶段3：进阶应用 (1-2周)
- [ ] 学习参数详解
- [ ] 优化训练配置
- [ ] 处理专业任务
- [ ] 使用高级功能

### 阶段4：专家级别 (持续)
- [ ] 自定义训练流程
- [ ] 开发新的配置模板
- [ ] 贡献社区
- [ ] 研究前沿技术

---

## 🔗 相关资源

### 官方资源
- **GitHub仓库**: https://github.com/hiyouga/LLaMA-Factory
- **官方文档**: https://llamafactory.readthedocs.io/
- **发布页面**: https://github.com/hiyouga/LLaMA-Factory/releases

### 社区资源
- **讨论区**: https://github.com/hiyouga/LLaMA-Factory/discussions
- **问题反馈**: https://github.com/hiyouga/LLaMA-Factory/issues
- **示例代码**: https://github.com/hiyouga/LLaMA-Factory/tree/main/examples

### 模型资源
- **HuggingFace模型**: https://huggingface.co/models
- **ModelScope模型**: https://modelscope.cn/models
- **数据集**: https://huggingface.co/datasets

---

## 📞 获取帮助

### 问题分类
1. **安装问题** → 查看完整用户指南的环境配置章节
2. **配置问题** → 参考参数详解手册
3. **训练问题** → 查看故障排除指南
4. **效果问题** → 参考实战案例和参数调优建议

### 求助渠道
1. **文档查阅** → 本文档集合
2. **社区讨论** → GitHub Discussions
3. **问题报告** → GitHub Issues
4. **实时交流** → 相关技术群组

---

## 🎉 开始您的LLaMA-Factory之旅

1. **选择起点**: 根据您的经验选择合适的文档
2. **准备环境**: 按照指南配置开发环境
3. **选择配置**: 根据任务和资源选择配置模板
4. **开始训练**: 运行您的第一个训练任务
5. **持续学习**: 深入了解高级功能和优化技巧

**祝您训练愉快，模型效果优异！🚀**

---

## 📝 文档更新日志

- **v1.0** (2025-01-07): 初始版本，包含完整文档集合
- 后续更新将根据LLaMA-Factory版本和用户反馈持续优化

**📧 如有建议或发现错误，欢迎反馈！**
