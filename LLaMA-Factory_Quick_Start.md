# 🚀 LLaMA-Factory 快速入门指南

## 🎯 5分钟快速上手

### 步骤1：安装LLaMA-Factory

```bash
# 克隆仓库
git clone https://github.com/hiyouga/LLaMA-Factory.git
cd LLaMA-Factory

# 安装依赖
pip install -e ".[torch,metrics]"
```

### 步骤2：启动Web界面

```bash
llamafactory-cli webui
```

访问 http://127.0.0.1:7860

### 步骤3：选择模型

在Web界面中：
1. 点击 **Train** 标签页
2. 在"模型名称"中输入：`Qwen/Qwen2.5-7B-Instruct`
3. 勾选"信任远程代码"

### 步骤4：配置训练

**基础配置：**
- 微调类型：`lora`
- 训练阶段：`sft`
- 数据集：`alpaca_en_demo`
- 模板：`qwen`

**训练参数：**
- 学习率：`1e-4`
- 训练轮数：`3.0`
- 批次大小：`2`
- 梯度累积：`8`

### 步骤5：开始训练

1. 设置输出目录：`saves/qwen2.5-7b/quick-start`
2. 点击"开始"按钮
3. 等待训练完成

---

## 📊 常用配置模板

### 🔥 推荐配置1：轻量级训练（4-8GB显存）

```yaml
# quick_start_light.yaml
model_name_or_path: Qwen/Qwen2.5-7B-Instruct
quantization_bit: 4
quantization_method: bnb
trust_remote_code: true

stage: sft
do_train: true
finetuning_type: lora
lora_rank: 8
lora_target: all

dataset: alpaca_en_demo
template: qwen
cutoff_len: 1024
max_samples: 1000

per_device_train_batch_size: 1
gradient_accumulation_steps: 16
learning_rate: 2e-4
num_train_epochs: 3.0
lr_scheduler_type: cosine
warmup_ratio: 0.1

output_dir: saves/qwen2.5-7b/light
logging_steps: 10
save_steps: 500
plot_loss: true
```

**使用方法：**
```bash
llamafactory-cli train quick_start_light.yaml
```

### ⚡ 推荐配置2：标准训练（16GB显存）

```yaml
# quick_start_standard.yaml
model_name_or_path: Qwen/Qwen2.5-7B-Instruct
trust_remote_code: true

stage: sft
do_train: true
finetuning_type: lora
lora_rank: 16
lora_alpha: 32
lora_target: all

dataset: alpaca_en_demo,identity
template: qwen
cutoff_len: 2048
max_samples: 5000

per_device_train_batch_size: 4
gradient_accumulation_steps: 4
learning_rate: 1e-4
num_train_epochs: 3.0
lr_scheduler_type: cosine
warmup_ratio: 0.1
bf16: true

output_dir: saves/qwen2.5-7b/standard
logging_steps: 10
save_steps: 500
plot_loss: true
```

### 🎯 推荐配置3：高质量训练（24GB+显存）

```yaml
# quick_start_premium.yaml
model_name_or_path: Qwen/Qwen2.5-7B-Instruct
trust_remote_code: true

stage: sft
do_train: true
finetuning_type: lora
lora_rank: 64
lora_alpha: 128
lora_target: all
use_dora: true

dataset: alpaca_en_demo,identity
template: qwen
cutoff_len: 4096
max_samples: 10000

per_device_train_batch_size: 8
gradient_accumulation_steps: 2
learning_rate: 5e-5
num_train_epochs: 5.0
lr_scheduler_type: cosine
warmup_ratio: 0.1
bf16: true

output_dir: saves/qwen2.5-7b/premium
logging_steps: 10
save_steps: 200
plot_loss: true
```

---

## 🛠️ 一键训练脚本

### 脚本1：自动检测显存并选择配置

```bash
#!/bin/bash
# auto_train.sh

# 检测显存大小
GPU_MEMORY=$(nvidia-smi --query-gpu=memory.total --format=csv,noheader,nounits | head -n1)

echo "检测到GPU显存: ${GPU_MEMORY}MB"

if [ $GPU_MEMORY -lt 8000 ]; then
    echo "使用轻量级配置..."
    CONFIG="quick_start_light.yaml"
elif [ $GPU_MEMORY -lt 20000 ]; then
    echo "使用标准配置..."
    CONFIG="quick_start_standard.yaml"
else
    echo "使用高质量配置..."
    CONFIG="quick_start_premium.yaml"
fi

echo "开始训练，配置文件: $CONFIG"
llamafactory-cli train $CONFIG
```

### 脚本2：批量实验

```bash
#!/bin/bash
# batch_experiment.sh

MODELS=("Qwen/Qwen2.5-7B-Instruct" "meta-llama/Meta-Llama-3-8B-Instruct")
DATASETS=("alpaca_en_demo" "identity")
LEARNING_RATES=("1e-4" "5e-5")

for model in "${MODELS[@]}"; do
    for dataset in "${DATASETS[@]}"; do
        for lr in "${LEARNING_RATES[@]}"; do
            echo "训练: $model + $dataset + $lr"
            
            OUTPUT_DIR="saves/experiments/$(basename $model)_${dataset}_${lr}"
            
            llamafactory-cli train \
                --model_name_or_path "$model" \
                --stage sft \
                --do_train \
                --finetuning_type lora \
                --lora_rank 16 \
                --dataset "$dataset" \
                --template qwen \
                --learning_rate "$lr" \
                --num_train_epochs 3.0 \
                --output_dir "$OUTPUT_DIR" \
                --per_device_train_batch_size 2 \
                --gradient_accumulation_steps 8 \
                --save_steps 500 \
                --logging_steps 10
        done
    done
done
```

---

## 📈 训练监控

### 实时监控脚本

```python
# monitor.py
import json
import time
import matplotlib.pyplot as plt
from pathlib import Path

def monitor_training(log_file="saves/qwen2.5-7b/quick-start/trainer_log.jsonl"):
    """实时监控训练进度"""
    plt.ion()  # 开启交互模式
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
    
    while True:
        if Path(log_file).exists():
            # 读取日志
            logs = []
            with open(log_file, 'r') as f:
                for line in f:
                    try:
                        logs.append(json.loads(line))
                    except:
                        continue
            
            if logs:
                # 提取数据
                train_logs = [log for log in logs if 'train_loss' in log]
                if train_logs:
                    steps = [log['step'] for log in train_logs]
                    losses = [log['train_loss'] for log in train_logs]
                    
                    # 绘制损失曲线
                    ax1.clear()
                    ax1.plot(steps, losses, 'b-', linewidth=2)
                    ax1.set_title(f'训练损失 (当前: {losses[-1]:.4f})')
                    ax1.set_xlabel('步数')
                    ax1.set_ylabel('损失')
                    ax1.grid(True)
                    
                    # 绘制学习率曲线
                    lrs = [log.get('learning_rate', 0) for log in train_logs]
                    ax2.clear()
                    ax2.plot(steps, lrs, 'r-', linewidth=2)
                    ax2.set_title('学习率变化')
                    ax2.set_xlabel('步数')
                    ax2.set_ylabel('学习率')
                    ax2.grid(True)
                    
                    plt.tight_layout()
                    plt.pause(1)
                    
                    print(f"步数: {steps[-1]}, 损失: {losses[-1]:.4f}, 学习率: {lrs[-1]:.2e}")
        
        time.sleep(10)  # 每10秒更新一次

if __name__ == "__main__":
    monitor_training()
```

### 使用方法

```bash
# 在另一个终端运行监控
python monitor.py
```

---

## 🎯 快速测试

### 训练完成后测试模型

```bash
# 启动对话测试
llamafactory-cli chat \
    --model_name_or_path Qwen/Qwen2.5-7B-Instruct \
    --adapter_name_or_path saves/qwen2.5-7b/magic_lab \
    --template qwen

# 或启动Web对话界面
llamafactory-cli webchat \
    --model_name_or_path Qwen/Qwen2.5-7B-Instruct \
    --adapter_name_or_path saves/qwen2.5-7b/magic_lab \
    --template qwen
```

### API服务部署

```bash
# 启动API服务
llamafactory-cli api \
    --model_name_or_path Qwen/Qwen2.5-7B-Instruct \
    --adapter_name_or_path saves/qwen2.5-7b/quick-start \
    --template qwen \
    --host 0.0.0.0 \
    --port 8000

# 测试API
curl -X POST "http://localhost:8000/v1/chat/completions" \
     -H "Content-Type: application/json" \
     -d '{
       "model": "qwen",
       "messages": [
         {"role": "user", "content": "你好，请介绍一下自己"}
       ],
       "temperature": 0.7,
       "max_tokens": 1000
     }'
```

---

## 🔧 常见问题快速解决

### Q1: 显存不足怎么办？

**解决方案：**
```yaml
# 减少批次大小
per_device_train_batch_size: 1
gradient_accumulation_steps: 16

# 启用量化
quantization_bit: 4
quantization_method: bnb

# 启用梯度检查点
gradient_checkpointing: true
```

### Q2: 训练速度太慢？

**解决方案：**
```yaml
# 减少序列长度
cutoff_len: 1024

# 减少样本数
max_samples: 1000

# 增加批次大小（如果显存允许）
per_device_train_batch_size: 4
```

### Q3: 模型效果不好？

**解决方案：**
```yaml
# 增加LoRA秩
lora_rank: 32

# 增加训练轮数
num_train_epochs: 5.0

# 使用更多数据
max_samples: 10000

# 尝试DoRA
use_dora: true
```

### Q4: 如何恢复中断的训练？

```bash
# 查找最新检查点
ls saves/qwen2.5-7b/quick-start/checkpoint-*

# 从检查点恢复
llamafactory-cli train quick_start.yaml \
    --resume_from_checkpoint saves/qwen2.5-7b/quick-start/checkpoint-1000
```

---

## 📚 下一步学习

1. **阅读完整指南**: `LLaMA-Factory_Complete_User_Guide.md`
2. **参数详解**: `LLaMA-Factory_Parameters_Reference.md`
3. **官方文档**: https://github.com/hiyouga/LLaMA-Factory
4. **社区讨论**: https://github.com/hiyouga/LLaMA-Factory/discussions

---

## 🎉 总结

通过这个快速入门指南，您应该能够：

- ✅ 在5分钟内启动第一次训练
- ✅ 根据显存选择合适的配置
- ✅ 监控训练进度
- ✅ 测试训练后的模型
- ✅ 解决常见问题

**现在开始您的LLaMA-Factory之旅吧！🚀**
