#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证新添加的OpenRouter和Poe服务商
"""

import sys
sys.path.append('src')

def verify_providers():
    """验证新服务商"""
    print("🔍 验证OpenRouter和Poe服务商...")
    
    try:
        # 测试导入
        from llamafactory.webui.components.simple_assistant import AI_PROVIDERS, validate_api_key
        print("✅ 模块导入成功")
        
        # 检查OpenRouter
        if "OpenRouter" in AI_PROVIDERS:
            or_config = AI_PROVIDERS["OpenRouter"]
            print(f"✅ OpenRouter: {len(or_config['models'])}个模型")
            print(f"   端点: {or_config['api_base']}")
        else:
            print("❌ OpenRouter配置缺失")
            return False
        
        # 检查Poe
        if "Poe" in AI_PROVIDERS:
            poe_config = AI_PROVIDERS["Poe"]
            print(f"✅ Poe: {len(poe_config['models'])}个模型")
            print(f"   端点: {poe_config['api_base']}")
        else:
            print("❌ Poe配置缺失")
            return False
        
        # 测试API密钥验证
        or_valid, or_msg = validate_api_key("sk-or-test123", "OpenRouter")
        poe_valid, poe_msg = validate_api_key("poe-test123456789012345", "Poe")
        
        print(f"✅ OpenRouter密钥验证: {or_msg}")
        print(f"✅ Poe密钥验证: {poe_msg}")
        
        print(f"\n📊 总服务商数量: {len(AI_PROVIDERS)}")
        print("🎉 新服务商验证通过！")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

if __name__ == "__main__":
    if verify_providers():
        print("\n🌟 OpenRouter和Poe服务商添加成功！")
        print("请在Web UI中体验新的服务商功能")
    else:
        print("\n❌ 服务商验证失败")
