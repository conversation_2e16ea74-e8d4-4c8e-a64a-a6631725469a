# 🤖 LLaMA-Factory 智能AI助手使用指南

## 📋 概述

智能AI助手是为LLaMA-Factory项目专门设计的浮窗组件，提供专业的模型微调咨询、训练辅助和故障排除服务。

## ✨ 核心特性

### 🎯 **专业咨询能力**
- **模型微调专家**：提供精确的参数配置建议
- **故障排除专家**：快速诊断训练问题
- **最佳实践指导**：基于项目经验的优化建议
- **数据处理专家**：指导数据集准备和格式化

### 🔧 **训练辅助功能**
- **硬件配置推荐**：根据GPU显存推荐最优参数
- **训练日志分析**：智能解读错误信息和性能指标
- **配置文件生成**：自动生成适合的训练配置
- **实时监控建议**：提供训练过程优化建议

### 🌐 **多模型支持**
- **OpenAI系列**：GPT-4, GPT-4 Turbo, GPT-3.5 Turbo
- **Claude系列**：Claude 3.5 Sonnet, Claude 3.5 Haiku
- **Gemini系列**：Gemini Pro, Gemini 1.5 Pro/Flash
- **xAI系列**：Grok Beta, Grok Vision Beta
- **国内模型**：
  - 智谱AI：GLM-4, GLM-4 Air/Flash/Plus
  - 阿里Qwen：Qwen Turbo/Plus/Max/Long
  - 百度文心：文心一言 4.0/3.5/Turbo
  - 讯飞星火：星火 3.5/Pro/Lite
- **本地模型**：支持自定义本地API服务

## 🚀 快速开始

### 1. 启动助手

1. 启动LLaMA-Factory Web UI：
   ```bash
   python src/webui.py
   ```

2. 在页面右下角找到 **"🤖 AI助手"** 按钮

3. 点击按钮打开智能助手浮窗

### 2. 配置AI模型

1. 点击 **"⚙️ 配置设置"** 标签页

2. 在 **"模型名称"** 输入框中输入您要使用的模型：
   ```
   # OpenAI示例
   gpt-4
   gpt-3.5-turbo
   
   # Claude示例  
   claude-3-5-sonnet-20241022
   claude-3-5-haiku-20241022
   
   # 智谱AI示例
   glm-4
   glm-4-air
   
   # Qwen示例
   qwen-turbo
   qwen-plus
   ```

3. 输入对应的 **API Key**

4. （可选）设置自定义 **API Base URL**

5. 调整 **温度参数** 和 **最大Token数**

6. 点击 **"💾 保存配置"**

### 3. 开始对话

#### 方式1：快捷问题
点击预设的快捷问题按钮：
- 🎯 如何选择合适的学习率？
- 🔧 LoRA参数推荐设置是什么？
- ⚡ QLoRA和LoRA有什么区别？
- 💾 如何处理显存不足的问题？
- 📊 训练数据集应该如何准备？

#### 方式2：自定义提问
在输入框中输入您的问题，例如：
```
我有一个8GB显存的RTX 3070，想要微调Qwen2.5-7B模型进行对话任务，应该如何配置参数？
```

## 🛠️ 训练工具使用

### 硬件配置推荐

1. 切换到 **"🛠️ 训练工具"** 标签页

2. 在 **"GPU显存大小"** 输入您的显存大小（GB）

3. 点击 **"获取推荐配置"**

4. 系统会根据您的硬件生成最优配置：

**4GB显存示例输出：**
```yaml
# GPU显存: 4GB 优化配置
quantization_bit: 4
lora_rank: 8
per_device_train_batch_size: 1
gradient_accumulation_steps: 16
cutoff_len: 512
```

### 训练日志分析

1. 将训练过程中的错误日志或输出复制到 **"训练日志"** 文本框

2. 点击 **"分析日志"**

3. 系统会智能分析并提供解决方案：

**示例分析结果：**
```markdown
## 📊 训练日志分析结果

🚨 **显存不足问题**
- 减少批次大小：`per_device_train_batch_size: 1`
- 增加梯度累积：`gradient_accumulation_steps: 16`
- 启用量化：`quantization_bit: 4`
- 启用梯度检查点：`gradient_checkpointing: true`
```

## 💡 使用技巧

### 1. 专业问题咨询

**参数配置类问题：**
```
我想训练一个医疗问答模型，数据集有5000条对话，使用16GB显存的V100，推荐什么参数配置？
```

**故障排除类问题：**
```
训练过程中出现"loss变为nan"的问题，可能是什么原因？如何解决？
```

**数据处理类问题：**
```
我有一批PDF文档想要转换为训练数据，应该如何处理和格式化？
```

### 2. 配置文件生成

**请求生成配置：**
```
请为我生成一个8GB显存的对话微调配置文件，使用Qwen2.5-7B模型
```

**自定义需求：**
```
我需要一个代码生成任务的配置，要求支持4096长度的序列，使用LoRA rank=32
```

### 3. 实时训练指导

**训练监控：**
```
我的训练损失在第500步后不再下降，应该如何调整？
```

**性能优化：**
```
训练速度很慢，每步需要30秒，有什么优化建议？
```

## 🔧 高级配置

### 本地模型配置

如果您使用本地部署的模型：

1. 在模型名称中输入：`custom`

2. 设置API Base URL为您的本地服务地址：
   ```
   http://localhost:8000/v1
   ```

3. API Key可以留空或输入本地服务的认证密钥

### 自定义系统提示词

智能助手使用专门的系统提示词来确保专业性，包含：
- LLaMA-Factory项目专业知识
- 模型微调技术细节
- 故障排除经验
- 最佳实践指导

### 配置持久化

所有配置会自动保存到 `smart_assistant_config.json` 文件中，包括：
- AI模型配置
- API密钥（加密存储）
- 聊天历史
- 用户偏好设置

## 🎨 界面特性

### 响应式设计
- **桌面端**：580px宽度，800px高度的浮窗
- **移动端**：自适应屏幕大小，95%宽度

### 美观界面
- **现代设计**：毛玻璃效果和渐变背景
- **流畅动画**：平滑的展开/收起动画
- **一致风格**：与LLaMA-Factory主界面保持一致

### 便捷操作
- **快捷键支持**：Enter键发送消息
- **一键清空**：快速清空对话历史
- **复制功能**：支持复制AI回复内容

## 🔍 故障排除

### 常见问题

**Q1: AI助手无响应**
- 检查API Key是否正确配置
- 确认网络连接正常
- 验证API端点地址是否正确

**Q2: 配置无法保存**
- 确保有文件写入权限
- 检查磁盘空间是否充足

**Q3: 界面显示异常**
- 刷新页面重新加载
- 清除浏览器缓存
- 检查浏览器兼容性

### 调试模式

如需调试，可以查看浏览器控制台的错误信息，或检查服务器日志。

## 📈 最佳实践

### 1. 提问技巧
- **具体描述**：提供详细的硬件配置和任务需求
- **包含上下文**：说明当前遇到的具体问题
- **分步提问**：复杂问题可以分解为多个小问题

### 2. 配置管理
- **定期备份**：备份配置文件以防丢失
- **分环境配置**：为不同项目使用不同配置
- **安全存储**：妥善保管API密钥

### 3. 效率提升
- **使用快捷问题**：常见问题直接点击按钮
- **保存常用配置**：为常用模型保存配置模板
- **批量咨询**：一次性询问多个相关问题

## 🎉 总结

智能AI助手为LLaMA-Factory用户提供了专业、便捷的技术支持，无论您是初学者还是专家，都能从中获得有价值的指导和帮助。

**立即体验智能AI助手，让您的模型微调之旅更加顺畅！** 🚀
