# 🔧 LLaMA-Factory 参数详解手册

## 📋 目录

- [1. 模型参数](#1-模型参数)
- [2. 训练参数](#2-训练参数)
- [3. Lo<PERSON>参数](#3-lora参数)
- [4. 数据参数](#4-数据参数)
- [5. 优化器参数](#5-优化器参数)
- [6. 量化参数](#6-量化参数)
- [7. 推理参数](#7-推理参数)
- [8. 高级参数](#8-高级参数)

---

## 1. 模型参数

### 1.1 基础模型配置

#### `model_name_or_path`
- **类型**: `str`
- **默认值**: 无
- **说明**: 预训练模型的名称或本地路径
- **示例**: 
  ```yaml
  model_name_or_path: Qwen/Qwen2.5-7B-Instruct
  model_name_or_path: /path/to/local/model
  ```

#### `trust_remote_code`
- **类型**: `bool`
- **默认值**: `false`
- **说明**: 是否信任远程代码执行
- **用途**: 某些模型需要自定义代码
- **示例**: 
  ```yaml
  trust_remote_code: true
  ```

#### `model_revision`
- **类型**: `str`
- **默认值**: `"main"`
- **说明**: 模型版本分支
- **示例**: 
  ```yaml
  model_revision: "v1.0"
  ```

#### `use_fast_tokenizer`
- **类型**: `bool`
- **默认值**: `true`
- **说明**: 是否使用快速分词器
- **影响**: 影响分词速度和内存使用

### 1.2 模型加载配置

#### `torch_dtype`
- **类型**: `str`
- **默认值**: `"auto"`
- **选项**: `"auto"`, `"float16"`, `"bfloat16"`, `"float32"`
- **说明**: 模型权重数据类型
- **推荐**: 
  - RTX 30/40系列: `"bfloat16"`
  - 其他GPU: `"float16"`

#### `device_map`
- **类型**: `str` 或 `dict`
- **默认值**: `"auto"`
- **说明**: 设备映射策略
- **选项**:
  ```yaml
  device_map: "auto"  # 自动分配
  device_map: "balanced"  # 平衡分配
  device_map: "sequential"  # 顺序分配
  ```

#### `low_cpu_mem_usage`
- **类型**: `bool`
- **默认值**: `true`
- **说明**: 降低CPU内存使用
- **用途**: 大模型加载优化

#### `use_cache`
- **类型**: `bool`
- **默认值**: `true`
- **说明**: 是否使用KV缓存
- **影响**: 推理速度vs显存使用

---

## 2. 训练参数

### 2.1 基础训练配置

#### `stage`
- **类型**: `str`
- **选项**: `"pt"`, `"sft"`, `"rm"`, `"ppo"`, `"dpo"`, `"kto"`
- **说明**: 训练阶段
- **详解**:
  - `pt`: 预训练 (Pre-training)
  - `sft`: 监督微调 (Supervised Fine-tuning)
  - `rm`: 奖励模型 (Reward Modeling)
  - `ppo`: 近端策略优化 (Proximal Policy Optimization)
  - `dpo`: 直接偏好优化 (Direct Preference Optimization)
  - `kto`: KTO算法

#### `do_train`
- **类型**: `bool`
- **默认值**: `false`
- **说明**: 是否执行训练

#### `do_eval`
- **类型**: `bool`
- **默认值**: `false`
- **说明**: 是否执行评估

#### `do_predict`
- **类型**: `bool`
- **默认值**: `false`
- **说明**: 是否执行预测

### 2.2 训练超参数

#### `learning_rate`
- **类型**: `float`
- **默认值**: `5e-5`
- **说明**: 学习率
- **推荐值**:
  - Full Fine-tuning: `1e-5` - `5e-5`
  - LoRA: `1e-4` - `5e-4`
  - QLoRA: `2e-4` - `1e-3`

#### `num_train_epochs`
- **类型**: `float`
- **默认值**: `3.0`
- **说明**: 训练轮数
- **推荐**:
  - 小数据集: 5-10轮
  - 大数据集: 1-3轮

#### `max_steps`
- **类型**: `int`
- **默认值**: `-1`
- **说明**: 最大训练步数 (-1表示使用epochs)

#### `per_device_train_batch_size`
- **类型**: `int`
- **默认值**: `8`
- **说明**: 每个设备的训练批次大小
- **推荐**:
  - 4GB显存: 1
  - 8GB显存: 2
  - 16GB显存: 4-8

#### `gradient_accumulation_steps`
- **类型**: `int`
- **默认值**: `1`
- **说明**: 梯度累积步数
- **计算**: 有效批次 = batch_size × accumulation_steps × GPU数

#### `warmup_ratio`
- **类型**: `float`
- **默认值**: `0.0`
- **说明**: 预热比例
- **推荐**: `0.05` - `0.1`

#### `warmup_steps`
- **类型**: `int`
- **默认值**: `0`
- **说明**: 预热步数 (与warmup_ratio二选一)

### 2.3 学习率调度

#### `lr_scheduler_type`
- **类型**: `str`
- **默认值**: `"linear"`
- **选项**: 
  - `"linear"`: 线性衰减
  - `"cosine"`: 余弦衰减
  - `"cosine_with_restarts"`: 带重启的余弦
  - `"polynomial"`: 多项式衰减
  - `"constant"`: 常数
  - `"constant_with_warmup"`: 带预热的常数

#### `cosine_restart_cycles`
- **类型**: `int`
- **默认值**: `1`
- **说明**: 余弦重启周期数

### 2.4 正则化参数

#### `weight_decay`
- **类型**: `float`
- **默认值**: `0.0`
- **说明**: 权重衰减
- **推荐**: `0.01` - `0.1`

#### `max_grad_norm`
- **类型**: `float`
- **默认值**: `1.0`
- **说明**: 梯度裁剪阈值

#### `label_smoothing_factor`
- **类型**: `float`
- **默认值**: `0.0`
- **说明**: 标签平滑因子
- **范围**: `0.0` - `0.1`

---

## 3. LoRA参数

### 3.1 核心LoRA配置

#### `finetuning_type`
- **类型**: `str`
- **选项**: `"full"`, `"freeze"`, `"lora"`
- **说明**: 微调类型
- **详解**:
  - `full`: 全参数微调
  - `freeze`: 冻结部分参数
  - `lora`: LoRA微调

#### `lora_rank`
- **类型**: `int`
- **默认值**: `8`
- **说明**: LoRA秩
- **影响**: 参数量和表达能力
- **推荐**:
  - 轻量任务: 4-8
  - 一般任务: 16-32
  - 复杂任务: 64-128

#### `lora_alpha`
- **类型**: `int`
- **默认值**: `None` (自动设为rank×2)
- **说明**: LoRA缩放因子
- **公式**: `scaling = lora_alpha / lora_rank`
- **推荐**: 通常设为rank的2倍

#### `lora_dropout`
- **类型**: `float`
- **默认值**: `0.0`
- **说明**: LoRA层的dropout率
- **推荐**: `0.0` - `0.3`

#### `lora_target`
- **类型**: `str`
- **默认值**: `"all"`
- **说明**: LoRA目标模块
- **选项**:
  ```yaml
  lora_target: "all"  # 所有线性层
  lora_target: "q_proj,v_proj"  # 注意力QV投影
  lora_target: "q_proj,v_proj,k_proj,o_proj"  # 完整注意力
  lora_target: "q_proj,v_proj,gate_proj,up_proj,down_proj"  # 注意力+FFN
  ```

### 3.2 高级LoRA配置

#### `additional_target`
- **类型**: `str`
- **默认值**: `None`
- **说明**: 额外的可训练模块
- **示例**: 
  ```yaml
  additional_target: "embed_tokens,lm_head"
  ```

#### `use_rslora`
- **类型**: `bool`
- **默认值**: `false`
- **说明**: 是否使用RSLoRA (秩稳定LoRA)
- **用途**: 提高训练稳定性

#### `use_dora`
- **类型**: `bool`
- **默认值**: `false`
- **说明**: 是否使用DoRA (权重分解LoRA)
- **效果**: 通常比标准LoRA效果更好

#### `loraplus_lr_ratio`
- **类型**: `float`
- **默认值**: `None`
- **说明**: LoRA+ B矩阵学习率比例
- **用途**: 差异化学习率优化

#### `loraplus_lr_embedding`
- **类型**: `float`
- **默认值**: `1e-6`
- **说明**: LoRA+ 嵌入层学习率

### 3.3 PiSSA配置

#### `use_pissa`
- **类型**: `bool`
- **默认值**: `false`
- **说明**: 是否使用PiSSA初始化

#### `pissa_iter`
- **类型**: `int`
- **默认值**: `16`
- **说明**: PiSSA迭代次数

#### `pissa_convert`
- **类型**: `bool`
- **默认值**: `false`
- **说明**: 训练后是否转换为标准LoRA

---

## 4. 数据参数

### 4.1 数据集配置

#### `dataset`
- **类型**: `str`
- **说明**: 数据集名称 (多个用逗号分隔)
- **示例**: 
  ```yaml
  dataset: "alpaca_en_demo"
  dataset: "dataset1,dataset2,dataset3"
  ```

#### `dataset_dir`
- **类型**: `str`
- **默认值**: `"data"`
- **说明**: 数据集目录路径

#### `template`
- **类型**: `str`
- **说明**: 对话模板
- **常用模板**:
  - `llama3`: LLaMA-3模板
  - `qwen`: Qwen模板
  - `chatglm3`: ChatGLM3模板
  - `baichuan2`: Baichuan2模板

#### `cutoff_len`
- **类型**: `int`
- **默认值**: `1024`
- **说明**: 最大序列长度
- **推荐**:
  - 对话任务: 1024-2048
  - 长文本: 4096-8192

#### `max_samples`
- **类型**: `int`
- **默认值**: `None`
- **说明**: 最大样本数 (None表示使用全部)

### 4.2 数据处理配置

#### `overwrite_cache`
- **类型**: `bool`
- **默认值**: `false`
- **说明**: 是否覆盖缓存

#### `preprocessing_num_workers`
- **类型**: `int`
- **默认值**: `None`
- **说明**: 预处理工作进程数
- **推荐**: CPU核心数的1-2倍

#### `dataloader_num_workers`
- **类型**: `int`
- **默认值**: `0`
- **说明**: 数据加载工作进程数

#### `streaming`
- **类型**: `bool`
- **默认值**: `false`
- **说明**: 是否使用流式数据加载
- **用途**: 大数据集内存优化

#### `buffer_size`
- **类型**: `int`
- **默认值**: `16384`
- **说明**: 流式加载缓冲区大小

### 4.3 数据增强

#### `neat_packing`
- **类型**: `bool`
- **默认值**: `false`
- **说明**: 是否使用紧凑打包
- **用途**: 提高序列利用率

#### `packing`
- **类型**: `bool`
- **默认值**: `false`
- **说明**: 是否打包多个样本

---

## 5. 优化器参数

### 5.1 优化器选择

#### `optim`
- **类型**: `str`
- **默认值**: `"adamw_torch"`
- **选项**:
  - `"adamw_torch"`: PyTorch AdamW
  - `"adamw_hf"`: HuggingFace AdamW
  - `"adafactor"`: Adafactor
  - `"sgd"`: SGD
  - `"adamw_8bit"`: 8位AdamW
  - `"adamw_bnb_8bit"`: BitsAndBytes 8位AdamW
  - `"lion_8bit"`: 8位Lion
  - `"paged_adamw_8bit"`: 分页8位AdamW

#### `adam_beta1`
- **类型**: `float`
- **默认值**: `0.9`
- **说明**: Adam优化器beta1参数

#### `adam_beta2`
- **类型**: `float`
- **默认值**: `0.999`
- **说明**: Adam优化器beta2参数

#### `adam_epsilon`
- **类型**: `float`
- **默认值**: `1e-8`
- **说明**: Adam优化器epsilon参数

### 5.2 GaLore配置

#### `use_galore`
- **类型**: `bool`
- **默认值**: `false`
- **说明**: 是否使用GaLore优化

#### `galore_rank`
- **类型**: `int`
- **默认值**: `16`
- **说明**: GaLore秩

#### `galore_update_interval`
- **类型**: `int`
- **默认值**: `200`
- **说明**: GaLore更新间隔

#### `galore_scale`
- **类型**: `float`
- **默认值**: `2.0`
- **说明**: GaLore缩放系数

#### `galore_target`
- **类型**: `str`
- **默认值**: `"all"`
- **说明**: GaLore目标模块

---

## 6. 量化参数

### 6.1 量化配置

#### `quantization_bit`
- **类型**: `int`
- **选项**: `4`, `8`
- **说明**: 量化位数
- **影响**: 显存使用和精度

#### `quantization_method`
- **类型**: `str`
- **选项**: `"bnb"`, `"hqq"`, `"eetq"`
- **说明**: 量化方法
- **对比**:
  - `bnb`: BitsAndBytes，兼容性最好
  - `hqq`: Half-Quadratic Quantization，速度快
  - `eetq`: Easy and Efficient Quantization，推理快

#### `double_quantization`
- **类型**: `bool`
- **默认值**: `true`
- **说明**: 是否使用双重量化 (仅BnB)

#### `quantization_type`
- **类型**: `str`
- **默认值**: `"nf4"`
- **选项**: `"fp4"`, `"nf4"`
- **说明**: 量化数据类型 (仅BnB)

### 6.2 HQQ特定参数

#### `hqq_axis`
- **类型**: `int`
- **默认值**: `0`
- **说明**: HQQ量化轴

#### `hqq_dynamic`
- **类型**: `bool`
- **默认值**: `false`
- **说明**: 是否使用动态HQQ

---

## 7. 推理参数

### 7.1 生成配置

#### `do_sample`
- **类型**: `bool`
- **默认值**: `true`
- **说明**: 是否使用采样生成

#### `temperature`
- **类型**: `float`
- **默认值**: `0.95`
- **说明**: 采样温度
- **范围**: `0.1` - `2.0`
- **影响**: 生成随机性

#### `top_p`
- **类型**: `float`
- **默认值**: `0.7`
- **说明**: 核采样概率阈值
- **范围**: `0.1` - `1.0`

#### `top_k`
- **类型**: `int`
- **默认值**: `50`
- **说明**: Top-K采样
- **范围**: `1` - `100`

#### `max_new_tokens`
- **类型**: `int`
- **默认值**: `512`
- **说明**: 最大生成token数

#### `repetition_penalty`
- **类型**: `float`
- **默认值**: `1.0`
- **说明**: 重复惩罚
- **推荐**: `1.0` - `1.2`

### 7.2 推理后端

#### `infer_backend`
- **类型**: `str`
- **默认值**: `"huggingface"`
- **选项**: `"huggingface"`, `"vllm"`, `"sglang"`
- **说明**: 推理后端选择

#### `vllm_enforce_eager`
- **类型**: `bool`
- **默认值**: `false`
- **说明**: VLLM强制eager模式

#### `vllm_max_lora_rank`
- **类型**: `int`
- **默认值**: `32`
- **说明**: VLLM最大LoRA秩

---

## 8. 高级参数

### 8.1 内存优化

#### `gradient_checkpointing`
- **类型**: `bool`
- **默认值**: `false`
- **说明**: 是否使用梯度检查点
- **效果**: 节省显存，略微降低速度

#### `dataloader_pin_memory`
- **类型**: `bool`
- **默认值**: `true`
- **说明**: 是否固定内存

#### `dataloader_persistent_workers`
- **类型**: `bool`
- **默认值**: `false`
- **说明**: 是否保持工作进程

### 8.2 混合精度

#### `fp16`
- **类型**: `bool`
- **默认值**: `false`
- **说明**: 是否使用FP16

#### `bf16`
- **类型**: `bool`
- **默认值**: `false`
- **说明**: 是否使用BF16 (推荐)

#### `fp16_opt_level`
- **类型**: `str`
- **默认值**: `"O1"`
- **说明**: FP16优化级别

### 8.3 分布式训练

#### `ddp_timeout`
- **类型**: `int`
- **默认值**: `1800`
- **说明**: DDP超时时间 (秒)

#### `ddp_backend`
- **类型**: `str`
- **默认值**: `"nccl"`
- **说明**: DDP后端

#### `ddp_find_unused_parameters`
- **类型**: `bool`
- **默认值**: `false`
- **说明**: 是否查找未使用参数

### 8.4 日志和保存

#### `logging_steps`
- **类型**: `int`
- **默认值**: `500`
- **说明**: 日志记录间隔

#### `save_steps`
- **类型**: `int`
- **默认值**: `500`
- **说明**: 模型保存间隔

#### `save_total_limit`
- **类型**: `int`
- **默认值**: `None`
- **说明**: 最大保存检查点数

#### `save_only_model`
- **类型**: `bool`
- **默认值**: `false`
- **说明**: 是否只保存模型权重

#### `plot_loss`
- **类型**: `bool`
- **默认值**: `false`
- **说明**: 是否绘制损失曲线

#### `report_to`
- **类型**: `str`
- **默认值**: `"none"`
- **选项**: `"none"`, `"wandb"`, `"tensorboard"`, `"swanlab"`, `"mlflow"`
- **说明**: 实验跟踪工具

---

## 📚 参数组合建议

### 快速实验配置
```yaml
# 适合快速验证想法
per_device_train_batch_size: 1
gradient_accumulation_steps: 8
learning_rate: 2e-4
num_train_epochs: 1.0
lora_rank: 8
quantization_bit: 4
```

### 平衡配置
```yaml
# 效果与效率平衡
per_device_train_batch_size: 2
gradient_accumulation_steps: 8
learning_rate: 1e-4
num_train_epochs: 3.0
lora_rank: 16
bf16: true
```

### 高质量配置
```yaml
# 追求最佳效果
per_device_train_batch_size: 4
gradient_accumulation_steps: 4
learning_rate: 5e-5
num_train_epochs: 5.0
lora_rank: 64
use_dora: true
```

**📖 本手册涵盖了LLaMA-Factory的所有重要参数，建议根据具体任务和资源情况选择合适的配置！**
