#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能AI助手功能测试脚本
测试核心功能是否正常工作
"""

import sys
import os

# 添加项目路径
sys.path.append('src')

def test_hardware_recommendations():
    """测试硬件推荐功能"""
    print("🔧 测试硬件推荐功能...")
    
    try:
        from llamafactory.webui.components.smart_assistant import get_hardware_recommendations
        
        # 测试不同显存大小
        test_cases = [4, 8, 16, 24]
        
        for gpu_memory in test_cases:
            recommendations = get_hardware_recommendations(gpu_memory)
            print(f"\n📊 {gpu_memory}GB显存推荐:")
            print(f"   描述: {recommendations['recommendation']}")
            print(f"   LoRA Rank: {recommendations.get('lora_rank', 'N/A')}")
            print(f"   批次大小: {recommendations.get('per_device_train_batch_size', 'N/A')}")
            print(f"   量化位数: {recommendations.get('quantization_bit', 'N/A')}")
        
        print("✅ 硬件推荐功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 硬件推荐功能测试失败: {e}")
        return False

def test_log_analysis():
    """测试日志分析功能"""
    print("\n📊 测试日志分析功能...")
    
    try:
        from llamafactory.webui.components.smart_assistant import analyze_training_log
        
        # 测试不同类型的日志
        test_logs = [
            ("显存不足", "RuntimeError: CUDA out of memory. Tried to allocate 2.00 GiB"),
            ("NaN值问题", "Training loss became nan at step 100"),
            ("正常训练", "Training step 100, loss: 2.345, lr: 1e-4")
        ]
        
        for log_type, log_content in test_logs:
            analysis = analyze_training_log(log_content)
            print(f"\n🔍 {log_type}分析:")
            print(f"   日志: {log_content}")
            print(f"   分析: {analysis[:100]}...")
        
        print("✅ 日志分析功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 日志分析功能测试失败: {e}")
        return False

def test_config_management():
    """测试配置管理功能"""
    print("\n⚙️ 测试配置管理功能...")
    
    try:
        from llamafactory.webui.components.smart_assistant import load_config, save_config
        
        # 测试加载默认配置
        config = load_config()
        print(f"   默认提供商: {config.get('selected_provider', 'N/A')}")
        print(f"   默认模型: {config.get('selected_model', 'N/A')}")
        print(f"   温度参数: {config.get('temperature', 'N/A')}")
        
        # 测试保存配置
        test_config = {
            "selected_provider": "OpenAI",
            "selected_model": "gpt-4",
            "temperature": 0.8,
            "max_tokens": 1500
        }
        
        save_config(test_config)
        
        # 验证保存
        loaded_config = load_config()
        assert loaded_config["selected_provider"] == "OpenAI"
        assert loaded_config["selected_model"] == "gpt-4"
        
        print("✅ 配置管理功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置管理功能测试失败: {e}")
        return False

def test_model_configurations():
    """测试模型配置"""
    print("\n🤖 测试模型配置...")
    
    try:
        from llamafactory.webui.components.smart_assistant import AI_MODELS, API_ENDPOINTS
        
        print(f"   支持的AI服务商数量: {len(AI_MODELS)}")
        
        for provider, models in AI_MODELS.items():
            print(f"   {provider}: {len(models)}个模型")
            
        # 验证API端点配置完整性
        for provider in AI_MODELS.keys():
            assert provider in API_ENDPOINTS, f"缺少{provider}的API端点配置"
        
        print("✅ 模型配置测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 模型配置测试失败: {e}")
        return False

def test_quick_questions():
    """测试快捷问题"""
    print("\n🚀 测试快捷问题...")
    
    try:
        from llamafactory.webui.components.smart_assistant import QUICK_QUESTIONS
        
        print(f"   快捷问题数量: {len(QUICK_QUESTIONS)}")
        
        for i, question in enumerate(QUICK_QUESTIONS[:3], 1):
            print(f"   {i}. {question}")
        
        assert len(QUICK_QUESTIONS) > 0, "应该有快捷问题"
        
        print("✅ 快捷问题测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 快捷问题测试失败: {e}")
        return False

def test_system_prompt():
    """测试系统提示词"""
    print("\n💬 测试系统提示词...")
    
    try:
        from llamafactory.webui.components.smart_assistant import SYSTEM_PROMPT
        
        print(f"   提示词长度: {len(SYSTEM_PROMPT)}字符")
        
        # 检查关键词
        keywords = ["LLaMA-Factory", "微调", "专业", "助手"]
        for keyword in keywords:
            assert keyword in SYSTEM_PROMPT, f"系统提示词应包含'{keyword}'"
        
        print("✅ 系统提示词测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 系统提示词测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始智能AI助手功能测试...")
    print("=" * 50)
    
    tests = [
        test_hardware_recommendations,
        test_log_analysis,
        test_config_management,
        test_model_configurations,
        test_quick_questions,
        test_system_prompt
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！智能AI助手功能正常")
        print("\n📋 功能清单:")
        print("✅ 硬件配置推荐")
        print("✅ 训练日志分析")
        print("✅ 配置管理")
        print("✅ 多模型支持")
        print("✅ 快捷问题")
        print("✅ 专业提示词")
        
        print("\n🎊 智能AI助手已准备就绪！")
        print("💡 在Web UI右下角找到'🤖 AI助手'按钮开始使用")
        
        return True
    else:
        print(f"⚠️ 有 {total - passed} 个测试失败，请检查相关功能")
        return False

def demo_usage():
    """演示使用示例"""
    print("\n🎯 智能AI助手使用演示:")
    print("-" * 30)
    
    try:
        from llamafactory.webui.components.smart_assistant import (
            get_hardware_recommendations,
            analyze_training_log
        )
        
        # 演示硬件推荐
        print("\n💾 硬件推荐演示:")
        rec = get_hardware_recommendations(8)
        print(f"  8GB显存推荐: {rec['recommendation']}")
        print(f"  LoRA Rank: {rec.get('lora_rank')}")
        print(f"  批次大小: {rec.get('per_device_train_batch_size')}")
        
        # 演示日志分析
        print("\n📊 日志分析演示:")
        log = "CUDA out of memory"
        analysis = analyze_training_log(log)
        print(f"  日志: {log}")
        first_line = analysis.split('\n')[0] if '\n' in analysis else analysis[:50]
        print(f"  分析: {first_line}...")
        
    except Exception as e:
        print(f"演示失败: {e}")

if __name__ == "__main__":
    print("🤖 LLaMA-Factory 智能AI助手功能测试")
    print("=" * 50)
    
    # 运行测试
    success = run_all_tests()
    
    if success:
        # 演示使用
        demo_usage()
        
        print("\n🌟 测试完成！")
        print("📖 查看 Smart_AI_Assistant_Guide.md 获取详细使用指南")
    else:
        print("\n🛑 请修复测试中发现的问题")
        sys.exit(1)
    
    # 清理测试文件
    try:
        if os.path.exists("smart_assistant_config.json"):
            os.remove("smart_assistant_config.json")
            print("\n🧹 清理测试文件完成")
    except:
        pass
