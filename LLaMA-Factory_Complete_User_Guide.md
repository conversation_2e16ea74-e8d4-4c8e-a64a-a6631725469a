# 🚀 LLaMA-Factory 完整用户指南

## 📋 目录

- [1. 项目概述](#1-项目概述)
- [2. 环境配置与安装](#2-环境配置与安装)
- [3. Web UI 使用指南](#3-web-ui-使用指南)
- [4. 模型微调参数详解](#4-模型微调参数详解)
- [5. 数据准备与格式化](#5-数据准备与格式化)
- [6. 训练配置模板](#6-训练配置模板)
- [7. 操作流程标准化](#7-操作流程标准化)
- [8. 故障排除指南](#8-故障排除指南)

---

## 1. 项目概述

### 1.1 LLaMA-Factory 简介

LLaMA-Factory 是一个统一的大语言模型微调框架，支持多种训练方法和100+种预训练模型。

**核心特性：**
- 🎯 **多种训练方法**：SFT、DPO、PPO、KTO、RM、Pre-training
- 🔧 **高效微调**：LoRA、QLoRA、DoRA、GaLore
- 🌐 **Web界面**：直观的Gradio界面
- 📊 **实时监控**：训练过程可视化
- 🚀 **推理部署**：多种推理后端支持

### 1.2 支持的模型

**主流模型系列：**
- **LLaMA系列**：LLaMA-2, LLaMA-3, LLaMA-4
- **Qwen系列**：Qwen-1.5, Qwen-2, Qwen-2.5
- **ChatGLM系列**：ChatGLM-3, GLM-4
- **Baichuan系列**：Baichuan-2, Baichuan-3
- **其他**：Mistral, Gemma, Yi, DeepSeek等

### 1.3 训练方法对比

| 方法 | 显存需求 | 训练速度 | 效果 | 适用场景 |
|------|----------|----------|------|----------|
| **Full Fine-tuning** | 很高 | 慢 | 最佳 | 大数据集，充足资源 |
| **LoRA** | 低 | 快 | 良好 | 资源受限，快速实验 |
| **QLoRA** | 极低 | 中等 | 良好 | 消费级GPU |
| **DoRA** | 低 | 中等 | 优秀 | 平衡效果与效率 |

---

## 2. 环境配置与安装

### 2.1 系统要求

**硬件要求：**
- **GPU**：NVIDIA GPU (推荐RTX 3090/4090, A100, H100)
- **显存**：最低4GB，推荐16GB+
- **内存**：16GB+
- **存储**：50GB+ 可用空间

**软件要求：**
- **操作系统**：Linux (Ubuntu 20.04+), Windows 10+, macOS
- **Python**：3.8-3.11
- **CUDA**：11.8+ (GPU训练)

### 2.2 安装步骤

#### 方法1：从源码安装（推荐）

```bash
# 1. 克隆仓库
git clone https://github.com/hiyouga/LLaMA-Factory.git
cd LLaMA-Factory

# 2. 创建虚拟环境
conda create -n llamafactory python=3.10
conda activate llamafactory

# 3. 安装依赖
pip install -e ".[torch,metrics]"

# 4. 安装额外依赖（可选）
pip install "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git"  # Unsloth加速
pip install flash-attn --no-build-isolation  # Flash Attention
```

#### 方法2：使用pip安装

```bash
pip install llamafactory[torch,metrics]
```

#### 方法3：Docker安装

```bash
# 拉取镜像
docker pull hiyouga/llamafactory:latest

# 运行容器
docker run -it --gpus all \
    -v ./data:/app/data \
    -v ./saves:/app/saves \
    -p 7860:7860 \
    hiyouga/llamafactory:latest
```

### 2.3 验证安装

```bash
# 检查安装
llamafactory-cli version

# 启动Web UI
llamafactory-cli webui
```

---

## 3. Web UI 使用指南

### 3.1 启动Web界面

```bash
# 基本启动
llamafactory-cli webui

# 指定端口和主机
llamafactory-cli webui --host 0.0.0.0 --port 7860

# 启用分享链接
llamafactory-cli webui --share
```

### 3.2 界面功能模块

#### 3.2.1 Train 标签页

**功能**：模型训练配置和执行

**主要组件：**
- **模型选择**：选择预训练模型
- **训练方法**：SFT、DPO、PPO等
- **微调类型**：Full、LoRA、QLoRA
- **数据集配置**：选择和配置训练数据
- **训练参数**：学习率、批次大小等
- **输出设置**：保存路径、日志配置

#### 3.2.2 Evaluate & Predict 标签页

**功能**：模型评估和预测

**评估任务：**
- **MMLU**：多任务语言理解
- **C-Eval**：中文评估基准
- **GSM8K**：数学推理
- **HumanEval**：代码生成

#### 3.2.3 Chat 标签页

**功能**：与训练后的模型对话

**特性：**
- 实时对话
- 多轮对话支持
- 参数调节（温度、top-p等）
- 对话历史保存

#### 3.2.4 Export 标签页

**功能**：模型导出和合并

**导出选项：**
- **合并LoRA**：将LoRA权重合并到基础模型
- **量化导出**：导出量化模型
- **格式转换**：转换为不同格式

### 3.3 操作流程示例

#### 训练一个对话模型

1. **选择模型**：在"模型名称"中选择`Qwen2.5-7B-Instruct`
2. **设置路径**：填写模型路径或使用HuggingFace模型ID
3. **选择方法**：
   - 微调类型：`lora`
   - 训练阶段：`sft`
4. **配置数据**：
   - 数据集：`alpaca_en_demo`
   - 模板：`qwen`
5. **设置参数**：
   - 学习率：`1e-4`
   - 批次大小：`2`
   - 训练轮数：`3`
6. **开始训练**：点击"开始"按钮

---

## 4. 模型微调参数详解

### 4.1 核心训练参数

#### 4.1.1 学习率 (Learning Rate)

**作用**：控制模型参数更新的步长

**推荐值：**
- **Full Fine-tuning**：`1e-5` - `5e-5`
- **LoRA**：`1e-4` - `5e-4`
- **QLoRA**：`2e-4` - `1e-3`

**调节策略：**
```yaml
# 基础配置
learning_rate: 1e-4
lr_scheduler_type: cosine  # 选项：linear, cosine, polynomial
warmup_ratio: 0.1  # 预热比例
```

#### 4.1.2 批次大小 (Batch Size)

**作用**：每次训练使用的样本数量

**计算公式：**
```
有效批次大小 = per_device_train_batch_size × gradient_accumulation_steps × GPU数量
```

**推荐配置：**
```yaml
per_device_train_batch_size: 2  # 单GPU批次大小
gradient_accumulation_steps: 8  # 梯度累积步数
# 有效批次大小 = 2 × 8 = 16
```

#### 4.1.3 训练轮数 (Epochs)

**作用**：数据集完整训练的次数

**推荐值：**
- **小数据集** (<1K样本)：5-10轮
- **中等数据集** (1K-10K样本)：3-5轮
- **大数据集** (>10K样本)：1-3轮

### 4.2 LoRA参数详解

#### 4.2.1 LoRA Rank

**作用**：低秩分解的维度，控制参数量和表达能力

```yaml
lora_rank: 8  # 推荐值：4, 8, 16, 32, 64
```

**选择指南：**
- **Rank 4-8**：轻量级任务，快速实验
- **Rank 16-32**：平衡效果与效率
- **Rank 64+**：复杂任务，追求最佳效果

#### 4.2.2 LoRA Alpha

**作用**：缩放因子，控制LoRA权重的影响程度

```yaml
lora_alpha: 16  # 通常设为 lora_rank × 2
```

#### 4.2.3 LoRA Target

**作用**：指定应用LoRA的模块

```yaml
lora_target: all  # 选项：all, q_proj, v_proj, k_proj, o_proj, gate_proj, up_proj, down_proj
```

**常用配置：**
- **all**：所有线性层（推荐）
- **q_proj,v_proj**：仅注意力查询和值投影
- **q_proj,v_proj,k_proj,o_proj**：完整注意力模块

#### 4.2.4 LoRA Dropout

**作用**：防止过拟合

```yaml
lora_dropout: 0.1  # 推荐值：0.0-0.3
```

### 4.3 量化参数

#### 4.3.1 量化位数

```yaml
quantization_bit: 4  # 选项：4, 8
quantization_method: bnb  # 选项：bnb, hqq, eetq
```

#### 4.3.2 量化方法对比

| 方法 | 精度 | 速度 | 显存节省 | 兼容性 |
|------|------|------|----------|--------|
| **BitsAndBytes (bnb)** | 高 | 中等 | 50-75% | 最佳 |
| **HQQ** | 中等 | 快 | 60-80% | 良好 |
| **EETQ** | 高 | 最快 | 50% | 有限 |

### 4.4 显存优化配置

#### 4.4.1 不同显存的推荐配置

**4GB显存配置：**
```yaml
# 模型配置
quantization_bit: 4
quantization_method: bnb

# LoRA配置
lora_rank: 8
lora_target: q_proj,v_proj

# 训练配置
per_device_train_batch_size: 1
gradient_accumulation_steps: 16
max_grad_norm: 1.0
```

**8GB显存配置：**
```yaml
# 模型配置
quantization_bit: 4
quantization_method: bnb

# LoRA配置
lora_rank: 16
lora_target: all

# 训练配置
per_device_train_batch_size: 2
gradient_accumulation_steps: 8
```

**16GB显存配置：**
```yaml
# LoRA配置
lora_rank: 32
lora_target: all

# 训练配置
per_device_train_batch_size: 4
gradient_accumulation_steps: 4
bf16: true
```

**24GB+显存配置：**
```yaml
# 可选择Full Fine-tuning
finetuning_type: full  # 或 lora

# LoRA配置（如果使用）
lora_rank: 64
lora_target: all

# 训练配置
per_device_train_batch_size: 8
gradient_accumulation_steps: 2
bf16: true
```

### 4.5 高级优化参数

#### 4.5.1 混合精度训练

```yaml
bf16: true  # 推荐用于现代GPU
fp16: false  # 备选方案
```

#### 4.5.2 梯度检查点

```yaml
gradient_checkpointing: true  # 节省显存，略微降低速度
```

#### 4.5.3 DeepSpeed配置

```yaml
deepspeed: examples/deepspeed/ds_z3_config.json
```

**DeepSpeed配置文件示例：**
```json
{
  "train_batch_size": "auto",
  "train_micro_batch_size_per_gpu": "auto",
  "gradient_accumulation_steps": "auto",
  "gradient_clipping": 1.0,
  "zero_optimization": {
    "stage": 3,
    "offload_optimizer": {
      "device": "cpu"
    },
    "offload_param": {
      "device": "cpu"
    }
  },
  "bf16": {
    "enabled": true
  }
}
```

---

## 5. 数据准备与格式化

### 5.1 支持的数据格式

#### 5.1.1 Alpaca格式

**用途**：指令微调，单轮对话

**格式结构：**
```json
[
  {
    "instruction": "请解释什么是机器学习",
    "input": "",
    "output": "机器学习是人工智能的一个分支..."
  },
  {
    "instruction": "翻译以下句子",
    "input": "Hello, how are you?",
    "output": "你好，你好吗？"
  }
]
```

**字段说明：**
- `instruction`：指令或问题
- `input`：输入内容（可为空）
- `output`：期望的输出

#### 5.1.2 ShareGPT格式

**用途**：多轮对话，复杂交互

**格式结构：**
```json
[
  {
    "conversations": [
      {
        "from": "human",
        "value": "你好，请介绍一下自己"
      },
      {
        "from": "gpt",
        "value": "你好！我是一个AI助手..."
      },
      {
        "from": "human",
        "value": "你能帮我写代码吗？"
      },
      {
        "from": "gpt",
        "value": "当然可以！我可以帮你编写各种编程语言的代码..."
      }
    ]
  }
]
```

### 5.2 数据集配置

#### 5.2.1 dataset_info.json配置

```json
{
  "my_dataset": {
    "file_name": "my_data.json",
    "formatting": "alpaca",
    "columns": {
      "prompt": "instruction",
      "query": "input", 
      "response": "output"
    }
  },
  "my_chat_dataset": {
    "file_name": "my_chat_data.json",
    "formatting": "sharegpt",
    "columns": {
      "messages": "conversations"
    },
    "tags": {
      "role_tag": "from",
      "content_tag": "value",
      "user_tag": "human",
      "assistant_tag": "gpt"
    }
  }
}
```

### 5.3 数据预处理

#### 5.3.1 数据清洗脚本

```python
import json
import re

def clean_text(text):
    """清洗文本数据"""
    # 移除多余空白
    text = re.sub(r'\s+', ' ', text).strip()
    # 移除特殊字符
    text = re.sub(r'[^\w\s\u4e00-\u9fff.,!?;:]', '', text)
    return text

def process_alpaca_data(input_file, output_file):
    """处理Alpaca格式数据"""
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    processed_data = []
    for item in data:
        if len(item['output']) < 10:  # 过滤过短回复
            continue
            
        processed_item = {
            'instruction': clean_text(item['instruction']),
            'input': clean_text(item.get('input', '')),
            'output': clean_text(item['output'])
        }
        processed_data.append(processed_item)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(processed_data, f, ensure_ascii=False, indent=2)

# 使用示例
process_alpaca_data('raw_data.json', 'cleaned_data.json')
```

### 5.4 数据质量检查

#### 5.4.1 数据统计脚本

```python
import json
import matplotlib.pyplot as plt

def analyze_dataset(file_path):
    """分析数据集统计信息"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 统计信息
    total_samples = len(data)
    instruction_lengths = [len(item['instruction']) for item in data]
    output_lengths = [len(item['output']) for item in data]
    
    print(f"总样本数: {total_samples}")
    print(f"指令平均长度: {sum(instruction_lengths)/len(instruction_lengths):.1f}")
    print(f"输出平均长度: {sum(output_lengths)/len(output_lengths):.1f}")
    print(f"最长输出: {max(output_lengths)}")
    print(f"最短输出: {min(output_lengths)}")
    
    # 绘制长度分布图
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 2, 1)
    plt.hist(instruction_lengths, bins=50, alpha=0.7)
    plt.title('指令长度分布')
    plt.xlabel('字符数')
    plt.ylabel('频次')
    
    plt.subplot(1, 2, 2)
    plt.hist(output_lengths, bins=50, alpha=0.7)
    plt.title('输出长度分布')
    plt.xlabel('字符数')
    plt.ylabel('频次')
    
    plt.tight_layout()
    plt.savefig('dataset_analysis.png')
    plt.show()

# 使用示例
analyze_dataset('cleaned_data.json')
```

---

## 6. 训练配置模板

### 6.1 对话模型微调模板

#### 6.1.1 基础对话模型（LoRA）

```yaml
# llama3_chat_lora.yaml
### 模型配置
model_name_or_path: meta-llama/Meta-Llama-3-8B-Instruct
trust_remote_code: true

### 训练方法
stage: sft
do_train: true
finetuning_type: lora
lora_rank: 16
lora_alpha: 32
lora_target: all
lora_dropout: 0.1

### 数据集配置
dataset: alpaca_en_demo,identity
template: llama3
cutoff_len: 2048
max_samples: 10000
overwrite_cache: true
preprocessing_num_workers: 16

### 训练参数
per_device_train_batch_size: 2
gradient_accumulation_steps: 8
learning_rate: 1e-4
num_train_epochs: 3.0
lr_scheduler_type: cosine
warmup_ratio: 0.1
bf16: true

### 输出配置
output_dir: saves/llama3-8b/chat-lora
logging_steps: 10
save_steps: 500
save_total_limit: 3
plot_loss: true
overwrite_output_dir: true

### 评估配置
evaluation_strategy: steps
eval_steps: 500
per_device_eval_batch_size: 4
```

#### 6.1.2 量化微调模板（QLoRA）

```yaml
# qwen_chat_qlora.yaml
### 模型配置
model_name_or_path: Qwen/Qwen2.5-7B-Instruct
quantization_bit: 4
quantization_method: bnb
trust_remote_code: true

### 训练方法
stage: sft
do_train: true
finetuning_type: lora
lora_rank: 8
lora_alpha: 16
lora_target: all

### 数据集配置
dataset: my_chat_dataset
template: qwen
cutoff_len: 1024
max_samples: 5000

### 训练参数
per_device_train_batch_size: 1
gradient_accumulation_steps: 16
learning_rate: 2e-4
num_train_epochs: 5.0
lr_scheduler_type: cosine
warmup_ratio: 0.05

### 输出配置
output_dir: saves/qwen2.5-7b/chat-qlora
logging_steps: 5
save_steps: 200
```

### 6.2 代码生成模型模板

```yaml
# code_generation.yaml
### 模型配置
model_name_or_path: deepseek-ai/deepseek-coder-6.7b-instruct
trust_remote_code: true

### 训练方法
stage: sft
do_train: true
finetuning_type: lora
lora_rank: 32
lora_alpha: 64
lora_target: all

### 数据集配置
dataset: code_alpaca_en
template: deepseek
cutoff_len: 4096
max_samples: 20000

### 训练参数
per_device_train_batch_size: 4
gradient_accumulation_steps: 4
learning_rate: 5e-5
num_train_epochs: 2.0
lr_scheduler_type: linear
warmup_ratio: 0.1
bf16: true

### 输出配置
output_dir: saves/deepseek-coder/code-gen
```

### 6.3 DPO训练模板

```yaml
# dpo_training.yaml
### 模型配置
model_name_or_path: saves/llama3-8b/sft  # SFT训练后的模型
trust_remote_code: true

### 训练方法
stage: dpo
do_train: true
finetuning_type: lora
lora_rank: 16
lora_target: all
pref_beta: 0.1
pref_loss: sigmoid

### 数据集配置
dataset: dpo_en_demo
template: llama3
cutoff_len: 2048
max_samples: 5000

### 训练参数
per_device_train_batch_size: 1
gradient_accumulation_steps: 8
learning_rate: 5e-6
num_train_epochs: 3.0
lr_scheduler_type: cosine
warmup_ratio: 0.1

### 输出配置
output_dir: saves/llama3-8b/dpo
```

---

## 7. 操作流程标准化

### 7.1 完整训练流程

#### 7.1.1 准备阶段

**步骤1：环境检查**
```bash
# 检查CUDA
nvidia-smi

# 检查Python环境
python --version
pip list | grep torch

# 检查LLaMA-Factory
llamafactory-cli version
```

**步骤2：数据准备**
```bash
# 创建数据目录
mkdir -p data/my_project

# 准备数据文件
cp my_dataset.json data/my_project/

# 更新dataset_info.json
vim data/dataset_info.json
```

**步骤3：配置文件准备**
```bash
# 创建配置目录
mkdir -p configs/my_project

# 创建训练配置
vim configs/my_project/sft_config.yaml
```

#### 7.1.2 训练阶段

**步骤1：启动训练**
```bash
# 使用配置文件训练
llamafactory-cli train configs/my_project/sft_config.yaml

# 或使用命令行参数
llamafactory-cli train \
    --model_name_or_path Qwen/Qwen2.5-7B-Instruct \
    --stage sft \
    --do_train \
    --finetuning_type lora \
    --dataset my_dataset \
    --template qwen \
    --cutoff_len 1024 \
    --learning_rate 1e-4 \
    --num_train_epochs 3.0 \
    --output_dir saves/qwen2.5-7b/my_project
```

**步骤2：监控训练**
```bash
# 查看训练日志
tail -f saves/qwen2.5-7b/my_project/trainer_log.jsonl

# 使用TensorBoard监控
tensorboard --logdir saves/qwen2.5-7b/my_project

# 查看损失曲线
python -c "
import json
import matplotlib.pyplot as plt

# 读取训练日志
logs = []
with open('saves/qwen2.5-7b/my_project/trainer_log.jsonl', 'r') as f:
    for line in f:
        logs.append(json.loads(line))

# 绘制损失曲线
steps = [log['step'] for log in logs if 'train_loss' in log]
losses = [log['train_loss'] for log in logs if 'train_loss' in log]

plt.plot(steps, losses)
plt.title('Training Loss')
plt.xlabel('Steps')
plt.ylabel('Loss')
plt.savefig('training_loss.png')
plt.show()
"
```

#### 7.1.3 评估阶段

**步骤1：模型评估**
```bash
# 使用内置评估
llamafactory-cli eval \
    --model_name_or_path saves/qwen2.5-7b/my_project \
    --adapter_name_or_path saves/qwen2.5-7b/my_project \
    --template qwen \
    --task mmlu \
    --split test \
    --lang en \
    --n_shot 5 \
    --batch_size 4

# 自定义评估脚本
python scripts/evaluate_model.py \
    --model_path saves/qwen2.5-7b/my_project \
    --test_data data/test_set.json \
    --output_file evaluation_results.json
```

**步骤2：对话测试**
```bash
# 启动对话界面
llamafactory-cli chat \
    --model_name_or_path saves/qwen2.5-7b/my_project \
    --adapter_name_or_path saves/qwen2.5-7b/my_project \
    --template qwen

# 或使用Web界面
llamafactory-cli webchat \
    --model_name_or_path saves/qwen2.5-7b/my_project \
    --adapter_name_or_path saves/qwen2.5-7b/my_project \
    --template qwen
```

#### 7.1.4 部署阶段

**步骤1：模型导出**
```bash
# 合并LoRA权重
llamafactory-cli export \
    --model_name_or_path saves/qwen2.5-7b/my_project \
    --adapter_name_or_path saves/qwen2.5-7b/my_project \
    --template qwen \
    --finetuning_type lora \
    --export_dir exports/qwen2.5-7b-my_project

# 量化导出
llamafactory-cli export \
    --model_name_or_path saves/qwen2.5-7b/my_project \
    --adapter_name_or_path saves/qwen2.5-7b/my_project \
    --template qwen \
    --finetuning_type lora \
    --export_dir exports/qwen2.5-7b-my_project-int4 \
    --export_quantization_bit 4
```

**步骤2：API部署**
```bash
# 启动API服务
llamafactory-cli api \
    --model_name_or_path exports/qwen2.5-7b-my_project \
    --template qwen \
    --host 0.0.0.0 \
    --port 8000

# 测试API
curl -X POST "http://localhost:8000/v1/chat/completions" \
     -H "Content-Type: application/json" \
     -d '{
       "model": "qwen2.5-7b-my_project",
       "messages": [
         {"role": "user", "content": "你好，请介绍一下自己"}
       ],
       "temperature": 0.7,
       "max_tokens": 1000
     }'
```

### 7.2 批量训练脚本

```bash
#!/bin/bash
# batch_training.sh

# 配置参数
BASE_MODEL="Qwen/Qwen2.5-7B-Instruct"
DATASETS=("dataset1" "dataset2" "dataset3")
LEARNING_RATES=("1e-4" "5e-5" "2e-4")
LORA_RANKS=(8 16 32)

# 批量训练
for dataset in "${DATASETS[@]}"; do
    for lr in "${LEARNING_RATES[@]}"; do
        for rank in "${LORA_RANKS[@]}"; do
            echo "Training with dataset=$dataset, lr=$lr, rank=$rank"
            
            OUTPUT_DIR="saves/experiments/${dataset}_lr${lr}_rank${rank}"
            
            llamafactory-cli train \
                --model_name_or_path $BASE_MODEL \
                --stage sft \
                --do_train \
                --finetuning_type lora \
                --lora_rank $rank \
                --dataset $dataset \
                --template qwen \
                --learning_rate $lr \
                --num_train_epochs 3.0 \
                --output_dir $OUTPUT_DIR \
                --overwrite_output_dir \
                --save_steps 500 \
                --logging_steps 10
                
            echo "Training completed for $OUTPUT_DIR"
        done
    done
done

echo "All experiments completed!"
```

---

## 8. 故障排除指南

### 8.1 常见错误及解决方案

#### 8.1.1 显存不足错误

**错误信息：**
```
RuntimeError: CUDA out of memory
```

**解决方案：**
1. **减少批次大小**
   ```yaml
   per_device_train_batch_size: 1  # 从2减少到1
   gradient_accumulation_steps: 16  # 相应增加累积步数
   ```

2. **启用量化**
   ```yaml
   quantization_bit: 4
   quantization_method: bnb
   ```

3. **启用梯度检查点**
   ```yaml
   gradient_checkpointing: true
   ```

4. **使用DeepSpeed**
   ```yaml
   deepspeed: examples/deepspeed/ds_z3_config.json
   ```

#### 8.1.2 模型加载错误

**错误信息：**
```
OSError: Can't load tokenizer for 'model_name'
```

**解决方案：**
1. **检查模型路径**
   ```bash
   # 确认模型存在
   ls -la /path/to/model
   
   # 检查必要文件
   ls -la /path/to/model/tokenizer*
   ls -la /path/to/model/config.json
   ```

2. **设置信任远程代码**
   ```yaml
   trust_remote_code: true
   ```

3. **使用正确的模型ID**
   ```yaml
   # 正确格式
   model_name_or_path: Qwen/Qwen2.5-7B-Instruct
   
   # 或本地路径
   model_name_or_path: /path/to/local/model
   ```

#### 8.1.3 数据集加载错误

**错误信息：**
```
KeyError: 'dataset_name' not found in dataset_info.json
```

**解决方案：**
1. **检查dataset_info.json**
   ```bash
   # 查看数据集配置
   cat data/dataset_info.json | grep "dataset_name"
   ```

2. **添加数据集配置**
   ```json
   {
     "my_dataset": {
       "file_name": "my_data.json",
       "formatting": "alpaca"
     }
   }
   ```

3. **验证数据文件**
   ```bash
   # 检查数据文件格式
   head -n 5 data/my_data.json
   python -m json.tool data/my_data.json > /dev/null
   ```

#### 8.1.4 训练中断恢复

**场景：**训练过程中意外中断

**解决方案：**
1. **检查检查点**
   ```bash
   ls -la saves/model_name/checkpoint-*
   ```

2. **从检查点恢复**
   ```yaml
   resume_from_checkpoint: saves/model_name/checkpoint-1000
   ```

3. **自动恢复脚本**
   ```bash
   #!/bin/bash
   # auto_resume.sh
   
   OUTPUT_DIR="saves/qwen2.5-7b/my_project"
   
   # 查找最新检查点
   LATEST_CHECKPOINT=$(ls -1 $OUTPUT_DIR/checkpoint-* 2>/dev/null | tail -n 1)
   
   if [ -n "$LATEST_CHECKPOINT" ]; then
       echo "Resuming from $LATEST_CHECKPOINT"
       llamafactory-cli train configs/my_config.yaml \
           --resume_from_checkpoint $LATEST_CHECKPOINT
   else
       echo "No checkpoint found, starting fresh training"
       llamafactory-cli train configs/my_config.yaml
   fi
   ```

### 8.2 性能优化建议

#### 8.2.1 训练速度优化

1. **使用Flash Attention**
   ```bash
   pip install flash-attn --no-build-isolation
   ```

2. **启用编译优化**
   ```yaml
   torch_compile: true
   ```

3. **优化数据加载**
   ```yaml
   dataloader_num_workers: 8  # 根据CPU核心数调整
   preprocessing_num_workers: 16
   ```

#### 8.2.2 显存优化

1. **使用Unsloth加速**
   ```bash
   pip install "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git"
   ```

2. **启用CPU卸载**
   ```yaml
   deepspeed: examples/deepspeed/ds_z3_offload_config.json
   ```

### 8.3 调试工具

#### 8.3.1 训练监控脚本

```python
# monitor_training.py
import json
import time
import matplotlib.pyplot as plt
from pathlib import Path

def monitor_training(log_file, refresh_interval=30):
    """实时监控训练进度"""
    log_path = Path(log_file)
    
    while True:
        if log_path.exists():
            logs = []
            with open(log_path, 'r') as f:
                for line in f:
                    try:
                        logs.append(json.loads(line))
                    except:
                        continue
            
            if logs:
                # 提取训练指标
                steps = [log.get('step', 0) for log in logs if 'train_loss' in log]
                losses = [log.get('train_loss', 0) for log in logs if 'train_loss' in log]
                
                if steps and losses:
                    # 绘制实时损失曲线
                    plt.clf()
                    plt.plot(steps, losses)
                    plt.title(f'Training Progress (Latest Loss: {losses[-1]:.4f})')
                    plt.xlabel('Steps')
                    plt.ylabel('Loss')
                    plt.grid(True)
                    plt.pause(0.1)
                    
                    print(f"Step: {steps[-1]}, Loss: {losses[-1]:.4f}")
        
        time.sleep(refresh_interval)

# 使用示例
if __name__ == "__main__":
    monitor_training("saves/model/trainer_log.jsonl")
```

#### 8.3.2 模型对比脚本

```python
# compare_models.py
import json
import pandas as pd
import matplotlib.pyplot as plt

def compare_training_runs(run_dirs):
    """对比多个训练运行的结果"""
    results = {}
    
    for run_dir in run_dirs:
        log_file = f"{run_dir}/trainer_log.jsonl"
        try:
            logs = []
            with open(log_file, 'r') as f:
                for line in f:
                    logs.append(json.loads(line))
            
            # 提取最终指标
            final_loss = None
            for log in reversed(logs):
                if 'train_loss' in log:
                    final_loss = log['train_loss']
                    break
            
            results[run_dir] = {
                'final_loss': final_loss,
                'total_steps': len([l for l in logs if 'train_loss' in l])
            }
        except:
            print(f"Failed to load {log_file}")
    
    # 创建对比表格
    df = pd.DataFrame(results).T
    print("Training Results Comparison:")
    print(df)
    
    # 绘制对比图
    df['final_loss'].plot(kind='bar')
    plt.title('Final Training Loss Comparison')
    plt.ylabel('Loss')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('model_comparison.png')
    plt.show()

# 使用示例
run_directories = [
    "saves/experiment1",
    "saves/experiment2", 
    "saves/experiment3"
]
compare_training_runs(run_directories)
```

---

## 📚 附录

### A. 常用命令速查

```bash
# 训练相关
llamafactory-cli train config.yaml                    # 使用配置文件训练
llamafactory-cli train --help                         # 查看训练参数

# 评估相关  
llamafactory-cli eval --model_name_or_path MODEL      # 模型评估
llamafactory-cli chat --model_name_or_path MODEL      # 命令行对话

# 导出相关
llamafactory-cli export --help                        # 查看导出选项
llamafactory-cli api --model_name_or_path MODEL       # 启动API服务

# Web界面
llamafactory-cli webui                                # 启动Web界面
llamafactory-cli webchat --model_name_or_path MODEL   # Web对话界面
```

### B. 配置文件模板

**基础SFT配置：**
```yaml
# basic_sft.yaml
model_name_or_path: Qwen/Qwen2.5-7B-Instruct
stage: sft
do_train: true
finetuning_type: lora
lora_rank: 16
dataset: alpaca_en_demo
template: qwen
cutoff_len: 1024
learning_rate: 1e-4
num_train_epochs: 3.0
output_dir: saves/basic_sft
```

**量化训练配置：**
```yaml
# qlora_config.yaml
model_name_or_path: Qwen/Qwen2.5-7B-Instruct
quantization_bit: 4
quantization_method: bnb
stage: sft
do_train: true
finetuning_type: lora
lora_rank: 8
dataset: my_dataset
template: qwen
per_device_train_batch_size: 1
gradient_accumulation_steps: 16
output_dir: saves/qlora_training
```

### C. 资源链接

- **官方文档**: https://github.com/hiyouga/LLaMA-Factory
- **模型下载**: https://huggingface.co/models
- **数据集**: https://huggingface.co/datasets
- **社区讨论**: https://github.com/hiyouga/LLaMA-Factory/discussions

---

### D. 高级功能详解

#### D.1 多模态训练

**支持的模态：**
- **图像+文本**：LLaVA、Qwen-VL等
- **音频+文本**：Whisper微调
- **视频+文本**：Video-LLaVA

**多模态配置示例：**
```yaml
# multimodal_config.yaml
model_name_or_path: llava-hf/llava-1.5-7b-hf
stage: sft
do_train: true
finetuning_type: lora
dataset: mllm_demo
template: llava
cutoff_len: 2048
```

#### D.2 强化学习训练

**PPO训练流程：**
1. **SFT训练**：基础指令微调
2. **RM训练**：奖励模型训练
3. **PPO训练**：策略优化

**PPO配置示例：**
```yaml
# ppo_config.yaml
model_name_or_path: saves/llama3-8b/sft
reward_model: saves/llama3-8b/reward
stage: ppo
do_train: true
finetuning_type: lora
dataset: identity,alpaca_en_demo
```

#### D.3 分布式训练

**多GPU训练：**
```bash
# 使用torchrun
torchrun --nproc_per_node=4 src/train.py config.yaml

# 使用accelerate
accelerate launch src/train.py config.yaml
```

**DeepSpeed配置：**
```json
{
  "train_batch_size": "auto",
  "train_micro_batch_size_per_gpu": "auto",
  "gradient_accumulation_steps": "auto",
  "gradient_clipping": 1.0,
  "zero_optimization": {
    "stage": 3,
    "offload_optimizer": {"device": "cpu"},
    "offload_param": {"device": "cpu"}
  },
  "bf16": {"enabled": true}
}
```

### E. 实战案例

#### E.1 医疗问答模型训练

**数据准备：**
```json
[
  {
    "instruction": "患者症状分析",
    "input": "患者主诉：头痛、发热、咳嗽3天",
    "output": "根据症状描述，患者可能存在上呼吸道感染..."
  }
]
```

**训练配置：**
```yaml
# medical_qa.yaml
model_name_or_path: Qwen/Qwen2.5-7B-Instruct
stage: sft
do_train: true
finetuning_type: lora
lora_rank: 32
dataset: medical_qa_dataset
template: qwen
cutoff_len: 2048
learning_rate: 5e-5
num_train_epochs: 5.0
```

#### E.2 代码助手训练

**数据格式：**
```json
[
  {
    "instruction": "编写Python函数",
    "input": "实现一个计算斐波那契数列的函数",
    "output": "```python\ndef fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibonacci(n-2)\n```"
  }
]
```

**训练配置：**
```yaml
# code_assistant.yaml
model_name_or_path: deepseek-ai/deepseek-coder-6.7b-instruct
stage: sft
do_train: true
finetuning_type: lora
lora_rank: 64
dataset: code_dataset
template: deepseek
cutoff_len: 4096
```

### F. 性能基准测试

#### F.1 训练性能对比

| 配置 | 显存占用 | 训练速度 | 收敛轮数 | 最终效果 |
|------|----------|----------|----------|----------|
| **Full Fine-tuning** | 24GB | 慢 | 1-2轮 | 最佳 |
| **LoRA (rank=8)** | 8GB | 快 | 3-5轮 | 良好 |
| **LoRA (rank=32)** | 12GB | 中等 | 2-3轮 | 优秀 |
| **QLoRA (4bit)** | 6GB | 中等 | 3-5轮 | 良好 |

#### F.2 模型效果评估

**评估指标：**
- **BLEU分数**：文本生成质量
- **ROUGE分数**：摘要任务效果
- **准确率**：分类任务性能
- **困惑度**：语言模型质量

**评估脚本：**
```python
# evaluation.py
from transformers import AutoTokenizer, AutoModelForCausalLM
import torch
from datasets import load_dataset

def evaluate_model(model_path, test_data):
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    model = AutoModelForCausalLM.from_pretrained(model_path)

    total_loss = 0
    total_samples = 0

    for sample in test_data:
        inputs = tokenizer(sample['input'], return_tensors='pt')
        with torch.no_grad():
            outputs = model(**inputs, labels=inputs['input_ids'])
            total_loss += outputs.loss.item()
            total_samples += 1

    perplexity = torch.exp(torch.tensor(total_loss / total_samples))
    return perplexity.item()
```

### G. 故障排除进阶

#### G.1 内存泄漏问题

**症状：**训练过程中显存持续增长

**解决方案：**
```python
# 在训练循环中添加
import gc
import torch

# 每N步清理一次
if step % 100 == 0:
    gc.collect()
    torch.cuda.empty_cache()
```

#### G.2 梯度爆炸问题

**症状：**损失突然变为NaN或无穷大

**解决方案：**
```yaml
# 添加梯度裁剪
max_grad_norm: 1.0

# 降低学习率
learning_rate: 5e-5  # 从1e-4降低

# 使用更稳定的优化器
optim: adamw_torch
```

#### G.3 收敛问题

**症状：**损失不下降或震荡

**解决方案：**
1. **检查数据质量**
2. **调整学习率调度**
3. **增加warmup步数**
4. **检查批次大小**

### H. 最佳实践总结

#### H.1 训练前检查清单

- [ ] 数据格式正确
- [ ] 数据集大小合适
- [ ] 模型路径正确
- [ ] 显存配置合理
- [ ] 学习率设置适当
- [ ] 输出目录可写
- [ ] 备份重要数据

#### H.2 训练中监控要点

- [ ] 损失下降趋势
- [ ] 显存使用情况
- [ ] 训练速度稳定
- [ ] 定期保存检查点
- [ ] 验证集性能

#### H.3 训练后验证步骤

- [ ] 模型对话测试
- [ ] 基准评估
- [ ] 边界情况测试
- [ ] 性能对比
- [ ] 部署验证

**🎉 恭喜！您已完成LLaMA-Factory完整用户指南的学习。现在可以开始您的模型微调之旅了！**

---

## 📞 技术支持

如有问题，请通过以下方式获取帮助：
- **GitHub Issues**: https://github.com/hiyouga/LLaMA-Factory/issues
- **讨论区**: https://github.com/hiyouga/LLaMA-Factory/discussions
- **文档**: https://llamafactory.readthedocs.io/

**祝您训练愉快！🚀**
