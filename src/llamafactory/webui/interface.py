# Copyright 2025 the LlamaFactory team.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import os
import platform

from ..extras.misc import fix_proxy, is_env_enabled
from ..extras.packages import is_gradio_available
from .common import save_config
from .components import (
    create_smart_assistant,
    create_chat_box,
    create_eval_tab,
    create_export_tab,
    create_footer,
    create_infer_tab,
    create_top,
    create_train_tab,
)
from .css import CSS
from .engine import Engine


if is_gradio_available():
    import gradio as gr


def create_ui(demo_mode: bool = False) -> "gr.Blocks":
    engine = Engine(demo_mode=demo_mode, pure_chat=False)
    hostname = os.getenv("HOSTNAME", os.getenv("COMPUTERNAME", platform.node())).split(".")[0]

    with gr.Blocks(title=f"魔法实验室·医疗模型微调工坊 ({hostname})", css=CSS) as demo:
        # 添加居中显示的logo图片 - 更大尺寸版本
        # 获取项目根目录路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
        logo_path = os.path.join(project_root, "assets", "magic lab.png")

        # 使用Image组件显示logo，居中显示在标题上方，更大尺寸
        if os.path.exists(logo_path):
            logo_image = gr.Image(
                value=logo_path,
                show_label=False,
                show_download_button=False,
                show_share_button=False,
                interactive=False,
                elem_classes="centered-logo-image",
                container=False,
                width=200,
                height=133
            )
        else:
            # 如果图片不存在，使用HTML显示备用内容
            logo_image = gr.HTML(
                value="""
                <div class="centered-logo">
                    <div style="
                        background: linear-gradient(135deg, #4A90E2, #2E5BBA);
                        color: white;
                        padding: 12px 18px;
                        border-radius: 10px;
                        font-weight: bold;
                        text-align: center;
                        font-size: 18px;
                        box-shadow: 0 3px 12px rgba(0,0,0,0.15);
                        width: 200px;
                        height: 133px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    ">
                        🧪 Magic Lab
                    </div>
                </div>
                """,
                elem_classes="logo-container"
            )

        title = gr.HTML()
        subtitle = gr.HTML()
        if demo_mode:
            gr.DuplicateButton(value="Duplicate Space for private use", elem_classes="duplicate-button")

        engine.manager.add_elems("head", {"title": title, "subtitle": subtitle, "logo": logo_image})
        engine.manager.add_elems("top", create_top())
        lang: gr.Dropdown = engine.manager.get_elem_by_id("top.lang")

        with gr.Tab("Train"):
            engine.manager.add_elems("train", create_train_tab(engine))

        with gr.Tab("Evaluate & Predict"):
            engine.manager.add_elems("eval", create_eval_tab(engine))

        with gr.Tab("Chat"):
            engine.manager.add_elems("infer", create_infer_tab(engine))

        if not demo_mode:
            with gr.Tab("Export"):
                engine.manager.add_elems("export", create_export_tab(engine))

        with gr.Tab("🤖 AI助手"):
            engine.manager.add_elems("smart_assistant", create_smart_assistant())

        engine.manager.add_elems("footer", create_footer())

        demo.load(engine.resume, outputs=engine.manager.get_elem_list(), concurrency_limit=None)
        lang.change(engine.change_lang, [lang], engine.manager.get_elem_list(), queue=False)
        lang.input(save_config, inputs=[lang], queue=False)

    return demo


def create_web_demo() -> "gr.Blocks":
    engine = Engine(pure_chat=True)
    hostname = os.getenv("HOSTNAME", os.getenv("COMPUTERNAME", platform.node())).split(".")[0]

    with gr.Blocks(title=f"魔法实验室·医疗模型微调工坊 Web Demo ({hostname})", css=CSS) as demo:
        # 添加居中显示的logo图片 - 更大尺寸版本
        # 获取项目根目录路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
        logo_path = os.path.join(project_root, "assets", "magic lab.png")

        # 使用Image组件显示logo，居中显示在标题上方，更大尺寸
        if os.path.exists(logo_path):
            logo_image = gr.Image(
                value=logo_path,
                show_label=False,
                show_download_button=False,
                show_share_button=False,
                interactive=False,
                elem_classes="centered-logo-image",
                container=False,
                width=200,
                height=133
            )
        else:
            # 如果图片不存在，使用HTML显示备用内容
            logo_image = gr.HTML(
                value="""
                <div class="centered-logo">
                    <div style="
                        background: linear-gradient(135deg, #4A90E2, #2E5BBA);
                        color: white;
                        padding: 12px 18px;
                        border-radius: 10px;
                        font-weight: bold;
                        text-align: center;
                        font-size: 18px;
                        box-shadow: 0 3px 12px rgba(0,0,0,0.15);
                        width: 200px;
                        height: 133px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    ">
                        🧪 Magic Lab
                    </div>
                </div>
                """,
                elem_classes="logo-container"
            )

        lang = gr.Dropdown(choices=["en", "ru", "zh", "ko", "ja"], scale=1)
        engine.manager.add_elems("top", dict(lang=lang, logo=logo_image))

        _, _, chat_elems = create_chat_box(engine, visible=True)
        engine.manager.add_elems("infer", chat_elems)

        demo.load(engine.resume, outputs=engine.manager.get_elem_list(), concurrency_limit=None)
        lang.change(engine.change_lang, [lang], engine.manager.get_elem_list(), queue=False)
        lang.input(save_config, inputs=[lang], queue=False)

    return demo


def run_web_ui() -> None:
    gradio_ipv6 = is_env_enabled("GRADIO_IPV6")
    gradio_share = is_env_enabled("GRADIO_SHARE")
    server_name = os.getenv("GRADIO_SERVER_NAME", "[::]" if gradio_ipv6 else "0.0.0.0")
    print("Visit http://ip:port for Web UI, e.g., http://127.0.0.1:7860")
    fix_proxy(ipv6_enabled=gradio_ipv6)
    create_ui().queue().launch(share=gradio_share, server_name=server_name, inbrowser=True)


def run_web_demo() -> None:
    gradio_ipv6 = is_env_enabled("GRADIO_IPV6")
    gradio_share = is_env_enabled("GRADIO_SHARE")
    server_name = os.getenv("GRADIO_SERVER_NAME", "[::]" if gradio_ipv6 else "0.0.0.0")
    print("Visit http://ip:port for Web UI, e.g., http://127.0.0.1:7860")
    fix_proxy(ipv6_enabled=gradio_ipv6)
    create_web_demo().queue().launch(share=gradio_share, server_name=server_name, inbrowser=True)
