# Copyright 2025 the LlamaFactory team.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

CSS = r"""
/* 居中Logo样式 - 超大尺寸精致版本 */
.centered-logo {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  width: 100% !important;
  margin: 35px 0 45px 0 !important;
  padding: 0 !important;
  pointer-events: none !important;
  position: relative !important;
}

.centered-logo img {
  width: auto !important;
  height: auto !important;
  max-width: 350px !important;
  max-height: 233px !important;
  object-fit: contain !important;
  filter:
    drop-shadow(0 6px 25px rgba(0, 123, 191, 0.4))
    drop-shadow(0 2px 8px rgba(0, 123, 191, 0.6))
    brightness(1.05)
    contrast(1.1) !important;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border-radius: 15px !important;
}

.logo-container {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  width: 100% !important;
  margin: 35px 0 45px 0 !important;
  padding: 0 !important;
  pointer-events: none !important;
  position: relative !important;
}

/* 增强的logo背景光晕效果 */
.logo-container::before {
  content: '' !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  width: 420px !important;
  height: 280px !important;
  background:
    radial-gradient(ellipse, rgba(0, 123, 191, 0.12) 0%, rgba(0, 123, 191, 0.06) 40%, transparent 70%),
    radial-gradient(ellipse, rgba(255, 255, 255, 0.15) 0%, transparent 50%) !important;
  border-radius: 50% !important;
  z-index: -1 !important;
  pointer-events: none !important;
  animation: logo-glow 4s ease-in-out infinite alternate !important;
}

/* 添加光晕动画 */
@keyframes logo-glow {
  0% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.02);
  }
}

/* Gradio Image组件的居中样式 - 超大尺寸精致版本 */
.centered-logo-image {
  display: block !important;
  margin: 35px auto 45px auto !important;
  width: 350px !important;
  height: 233px !important;
  pointer-events: none !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  background:
    linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%),
    rgba(255, 255, 255, 0.02) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  box-shadow:
    0 6px 25px rgba(0, 123, 191, 0.4),
    0 12px 50px rgba(0, 123, 191, 0.2),
    0 2px 8px rgba(0, 123, 191, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.4) !important;
  border-radius: 15px !important;
  position: relative !important;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;
  overflow: hidden !important;
}

/* 增强的背景光晕 */
.centered-logo-image::before {
  content: '' !important;
  position: absolute !important;
  top: -15px !important;
  left: -15px !important;
  right: -15px !important;
  bottom: -15px !important;
  background:
    radial-gradient(ellipse, rgba(0, 123, 191, 0.12) 0%, rgba(0, 123, 191, 0.06) 40%, transparent 70%),
    radial-gradient(ellipse, rgba(255, 255, 255, 0.15) 0%, transparent 50%) !important;
  border-radius: 25px !important;
  z-index: -1 !important;
  pointer-events: none !important;
  animation: logo-glow 4s ease-in-out infinite alternate !important;
}

/* 添加玻璃效果装饰边框 */
.centered-logo-image::after {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 15px !important;
  pointer-events: none !important;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 30%,
    transparent 70%,
    rgba(255, 255, 255, 0.05) 100%) !important;
}

.centered-logo-image img {
  width: 100% !important;
  height: 100% !important;
  object-fit: contain !important;
  border-radius: 13px !important;
  filter: brightness(1.05) contrast(1.1) !important;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 医疗风格全局背景 */
.gradio-container {
  background: linear-gradient(135deg, #e8f4fd 0%, #f0f8ff 25%, #e6f7ff 50%, #f5fcff 75%, #ffffff 100%) !important;
  min-height: 100vh !important;
  position: relative !important;
}

/* 医疗风格背景纹理 */
.gradio-container::before {
  content: '' !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(0, 123, 191, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(0, 200, 150, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.05) 0%, transparent 50%) !important;
  pointer-events: none !important;
  z-index: -1 !important;
}

/* 主容器玻璃效果 */
.block.gradio-blocks {
  background: rgba(255, 255, 255, 0.25) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 16px !important;
  box-shadow:
    0 8px 32px rgba(0, 123, 191, 0.1),
    0 4px 16px rgba(0, 200, 150, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.4) !important;
  margin: 20px !important;
  padding: 20px !important;
}

/* Tab容器玻璃效果 */
.tab-nav {
  background: rgba(255, 255, 255, 0.4) !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  border: 1px solid rgba(0, 123, 191, 0.2) !important;
  border-radius: 12px 12px 0 0 !important;
  box-shadow:
    0 4px 16px rgba(0, 123, 191, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
}

/* Tab按钮样式 */
.tab-nav button {
  background: rgba(255, 255, 255, 0.3) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(0, 123, 191, 0.15) !important;
  border-radius: 8px !important;
  color: #1e40af !important;
  font-weight: 500 !important;
  margin: 4px !important;
  padding: 8px 16px !important;
  transition: all 0.3s ease !important;
  box-shadow:
    0 2px 8px rgba(0, 123, 191, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.4) !important;
}

/* Tab按钮悬停效果 */
.tab-nav button:hover {
  background: rgba(0, 123, 191, 0.1) !important;
  border-color: rgba(0, 123, 191, 0.3) !important;
  transform: translateY(-1px) !important;
  box-shadow:
    0 4px 12px rgba(0, 123, 191, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.6) !important;
}

/* 激活的Tab按钮 */
.tab-nav button.selected {
  background: rgba(0, 123, 191, 0.15) !important;
  border-color: rgba(0, 123, 191, 0.4) !important;
  color: #1e40af !important;
  font-weight: 600 !important;
  box-shadow:
    0 4px 16px rgba(0, 123, 191, 0.2),
    inset 0 2px 4px rgba(0, 123, 191, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.7) !important;
}

/* 输入框玻璃效果 */
input, textarea, select {
  background: rgba(255, 255, 255, 0.6) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(0, 123, 191, 0.2) !important;
  border-radius: 8px !important;
  color: #1e40af !important;
  box-shadow:
    0 2px 8px rgba(0, 123, 191, 0.05),
    inset 0 1px 2px rgba(0, 123, 191, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
  transition: all 0.3s ease !important;
}

/* 输入框焦点效果 */
input:focus, textarea:focus, select:focus {
  background: rgba(255, 255, 255, 0.8) !important;
  border-color: rgba(0, 123, 191, 0.4) !important;
  outline: none !important;
  box-shadow:
    0 4px 12px rgba(0, 123, 191, 0.1),
    0 0 0 3px rgba(0, 123, 191, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.7) !important;
}

/* 按钮玻璃效果 */
button {
  background: rgba(255, 255, 255, 0.4) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(0, 123, 191, 0.2) !important;
  border-radius: 8px !important;
  color: #1e40af !important;
  font-weight: 500 !important;
  padding: 8px 16px !important;
  transition: all 0.3s ease !important;
  box-shadow:
    0 2px 8px rgba(0, 123, 191, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.4) !important;
}

/* 按钮悬停效果 */
button:hover {
  background: rgba(0, 123, 191, 0.1) !important;
  border-color: rgba(0, 123, 191, 0.3) !important;
  transform: translateY(-1px) !important;
  box-shadow:
    0 4px 12px rgba(0, 123, 191, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.6) !important;
}

/* 主要按钮样式 */
button.primary {
  background: linear-gradient(135deg, rgba(0, 123, 191, 0.8) 0%, rgba(0, 200, 150, 0.8) 100%) !important;
  color: white !important;
  border-color: rgba(0, 123, 191, 0.4) !important;
  font-weight: 600 !important;
}

button.primary:hover {
  background: linear-gradient(135deg, rgba(0, 123, 191, 0.9) 0%, rgba(0, 200, 150, 0.9) 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow:
    0 6px 16px rgba(0, 123, 191, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

.duplicate-button {
  margin: auto !important;
  background: linear-gradient(135deg, rgba(0, 123, 191, 0.8) 0%, rgba(0, 200, 150, 0.8) 100%) !important;
  color: white !important;
  border: 1px solid rgba(0, 123, 191, 0.4) !important;
  border-radius: 100vh !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  box-shadow:
    0 4px 12px rgba(0, 123, 191, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

.thinking-summary {
  padding: 8px !important;
  background: rgba(255, 255, 255, 0.4) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(0, 123, 191, 0.15) !important;
  border-radius: 8px !important;
  box-shadow:
    0 2px 8px rgba(0, 123, 191, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.4) !important;
}

.thinking-summary span {
  border-radius: 4px !important;
  padding: 4px !important;
  cursor: pointer !important;
  background: rgba(0, 123, 191, 0.1) !important;
  color: #1e40af !important;
  border: 1px solid rgba(0, 123, 191, 0.2) !important;
  backdrop-filter: blur(5px) !important;
  -webkit-backdrop-filter: blur(5px) !important;
  transition: all 0.2s ease !important;
}

.thinking-summary span:hover {
  background: rgba(0, 123, 191, 0.15) !important;
  transform: translateY(-1px) !important;
}

.dark .thinking-summary span {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #93c5fd !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
}

.thinking-container {
  border-left: 2px solid rgba(0, 200, 150, 0.6) !important;
  padding-left: 10px !important;
  margin: 4px 0 !important;
  background: rgba(0, 200, 150, 0.05) !important;
  backdrop-filter: blur(5px) !important;
  -webkit-backdrop-filter: blur(5px) !important;
  border-radius: 0 8px 8px 0 !important;
}

.thinking-container p {
  color: #059669 !important;
}

.modal-box {
  position: fixed !important;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 1000px;
  max-height: 750px;
  overflow-y: auto;
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(0, 123, 191, 0.3) !important;
  border-radius: 16px !important;
  box-shadow:
    0 16px 48px rgba(0, 123, 191, 0.15),
    0 8px 24px rgba(0, 200, 150, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.6) !important;
  z-index: 1000;
  padding: 20px;
  flex-wrap: nowrap !important;
}

.dark .modal-box {
  background: rgba(30, 41, 59, 0.9) !important;
  border-color: rgba(148, 163, 184, 0.3) !important;
  color: #e2e8f0 !important;
}

/* 卡片容器玻璃效果 */
.gr-form, .gr-box {
  background: rgba(255, 255, 255, 0.3) !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  border: 1px solid rgba(0, 123, 191, 0.15) !important;
  border-radius: 12px !important;
  padding: 16px !important;
  margin: 8px 0 !important;
  box-shadow:
    0 4px 16px rgba(0, 123, 191, 0.08),
    0 2px 8px rgba(0, 200, 150, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.4) !important;
  transition: all 0.3s ease !important;
}

.gr-form:hover, .gr-box:hover {
  background: rgba(255, 255, 255, 0.4) !important;
  border-color: rgba(0, 123, 191, 0.25) !important;
  transform: translateY(-2px) !important;
  box-shadow:
    0 6px 20px rgba(0, 123, 191, 0.12),
    0 3px 12px rgba(0, 200, 150, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
}

/* 标签文字医疗风格 */
label {
  color: #1e40af !important;
  font-weight: 500 !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
}

/* 下拉菜单玻璃效果 */
.dropdown {
  background: rgba(255, 255, 255, 0.6) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(0, 123, 191, 0.2) !important;
  border-radius: 8px !important;
  box-shadow:
    0 2px 8px rgba(0, 123, 191, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
}

/* 进度条医疗风格 */
.progress {
  background: rgba(255, 255, 255, 0.4) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(0, 123, 191, 0.2) !important;
  border-radius: 8px !important;
  overflow: hidden !important;
}

.progress-bar {
  background: linear-gradient(90deg, rgba(0, 123, 191, 0.8) 0%, rgba(0, 200, 150, 0.8) 100%) !important;
  box-shadow:
    0 2px 8px rgba(0, 123, 191, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

/* 聊天框玻璃效果 */
.chatbot {
  background: rgba(255, 255, 255, 0.25) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(0, 123, 191, 0.2) !important;
  border-radius: 12px !important;
  box-shadow:
    0 8px 32px rgba(0, 123, 191, 0.1),
    0 4px 16px rgba(0, 200, 150, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.4) !important;
}

/* 消息气泡医疗风格 */
.message {
  background: rgba(255, 255, 255, 0.6) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(0, 123, 191, 0.15) !important;
  border-radius: 12px !important;
  margin: 8px 0 !important;
  padding: 12px !important;
  box-shadow:
    0 2px 8px rgba(0, 123, 191, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.4) !important;
}

.message.user {
  background: rgba(0, 123, 191, 0.1) !important;
  border-color: rgba(0, 123, 191, 0.25) !important;
  margin-left: 20% !important;
}

.message.bot {
  background: rgba(0, 200, 150, 0.1) !important;
  border-color: rgba(0, 200, 150, 0.25) !important;
  margin-right: 20% !important;
}

/* 滑块医疗风格 */
.slider {
  background: rgba(255, 255, 255, 0.4) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(0, 123, 191, 0.2) !important;
  border-radius: 8px !important;
}

.slider-thumb {
  background: linear-gradient(135deg, rgba(0, 123, 191, 0.9) 0%, rgba(0, 200, 150, 0.9) 100%) !important;
  border: 2px solid rgba(255, 255, 255, 0.8) !important;
  border-radius: 50% !important;
  box-shadow:
    0 4px 12px rgba(0, 123, 191, 0.3),
    0 2px 6px rgba(0, 200, 150, 0.2) !important;
}

/* 文件上传区域 */
.file-upload {
  background: rgba(255, 255, 255, 0.3) !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  border: 2px dashed rgba(0, 123, 191, 0.3) !important;
  border-radius: 12px !important;
  padding: 20px !important;
  text-align: center !important;
  transition: all 0.3s ease !important;
  box-shadow:
    0 4px 16px rgba(0, 123, 191, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.4) !important;
}

.file-upload:hover {
  background: rgba(0, 123, 191, 0.05) !important;
  border-color: rgba(0, 123, 191, 0.5) !important;
  transform: translateY(-2px) !important;
}

/* 状态指示器 */
.status-indicator {
  display: inline-block !important;
  width: 8px !important;
  height: 8px !important;
  border-radius: 50% !important;
  margin-right: 8px !important;
  box-shadow: 0 0 8px currentColor !important;
}

.status-success {
  background: #10b981 !important;
  color: #10b981 !important;
}

.status-warning {
  background: #f59e0b !important;
  color: #f59e0b !important;
}

.status-error {
  background: #ef4444 !important;
  color: #ef4444 !important;
}

/* 深色模式适配 */
.dark {
  background: linear-gradient(135deg, #1e293b 0%, #334155 25%, #475569 50%, #64748b 75%, #94a3b8 100%) !important;
}

.dark .gradio-container::before {
  background-image:
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(16, 185, 129, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.02) 0%, transparent 50%) !important;
}

.dark .block.gradio-blocks {
  background: rgba(30, 41, 59, 0.4) !important;
  border-color: rgba(148, 163, 184, 0.3) !important;
}

.dark label {
  color: #93c5fd !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
}

.dark input, .dark textarea, .dark select {
  background: rgba(30, 41, 59, 0.6) !important;
  color: #e2e8f0 !important;
  border-color: rgba(148, 163, 184, 0.3) !important;
}

.dark button {
  background: rgba(30, 41, 59, 0.6) !important;
  color: #e2e8f0 !important;
  border-color: rgba(148, 163, 184, 0.3) !important;
}

/* AI助手浮窗样式 */
.ai-assistant-trigger {
  position: fixed !important;
  bottom: 30px !important;
  right: 30px !important;
  width: 70px !important;
  height: 70px !important;
  border-radius: 50% !important;
  background: linear-gradient(135deg, #007bbf 0%, #00c896 100%) !important;
  color: white !important;
  border: 3px solid rgba(255, 255, 255, 0.4) !important;
  font-size: 28px !important;
  font-weight: bold !important;
  cursor: pointer !important;
  z-index: 10000 !important;
  box-shadow:
    0 12px 40px rgba(0, 123, 191, 0.4),
    0 6px 20px rgba(0, 200, 150, 0.3),
    inset 0 2px 8px rgba(255, 255, 255, 0.4) !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  animation: ai-pulse 3s infinite ease-in-out !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.ai-assistant-trigger:hover {
  transform: scale(1.15) translateY(-4px) !important;
  background: linear-gradient(135deg, #0087d1 0%, #00d4a8 100%) !important;
  box-shadow:
    0 16px 50px rgba(0, 123, 191, 0.5),
    0 8px 25px rgba(0, 200, 150, 0.4),
    inset 0 3px 12px rgba(255, 255, 255, 0.5) !important;
  animation: none !important;
  border-color: rgba(255, 255, 255, 0.6) !important;
}

@keyframes ai-pulse {
  0%, 100% {
    box-shadow:
      0 12px 40px rgba(0, 123, 191, 0.4),
      0 6px 20px rgba(0, 200, 150, 0.3),
      inset 0 2px 8px rgba(255, 255, 255, 0.4);
    transform: scale(1);
  }
  50% {
    box-shadow:
      0 16px 50px rgba(0, 123, 191, 0.5),
      0 8px 25px rgba(0, 200, 150, 0.4),
      inset 0 3px 12px rgba(255, 255, 255, 0.5);
    transform: scale(1.02);
  }
}

/* AI助手容器 */
.ai-assistant-container {
  position: fixed !important;
  bottom: 110px !important;
  right: 20px !important;
  width: 420px !important;
  max-width: calc(100vw - 40px) !important;
  max-height: calc(100vh - 150px) !important;
  height: auto !important;
  background: rgba(255, 255, 255, 0.96) !important;
  backdrop-filter: blur(25px) !important;
  -webkit-backdrop-filter: blur(25px) !important;
  border: 2px solid rgba(0, 123, 191, 0.25) !important;
  border-radius: 20px !important;
  box-shadow:
    0 20px 60px rgba(0, 123, 191, 0.25),
    0 10px 30px rgba(0, 200, 150, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.7),
    inset 0 -1px 0 rgba(0, 123, 191, 0.1) !important;
  z-index: 9999 !important;
  overflow: hidden !important;
  animation: ai-slide-in 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  display: flex !important;
  flex-direction: column !important;
}

@keyframes ai-slide-in {
  from {
    opacity: 0;
    transform: translateY(30px) translateX(20px) scale(0.9);
    filter: blur(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0) translateX(0) scale(1);
    filter: blur(0);
  }
}

/* AI助手头部 */
.ai-assistant-header {
  background: linear-gradient(135deg, rgba(0, 123, 191, 0.08) 0%, rgba(0, 200, 150, 0.08) 100%) !important;
  padding: 16px 20px !important;
  border-bottom: 1px solid rgba(0, 123, 191, 0.15) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  cursor: move !important;
  border-radius: 20px 20px 0 0 !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  flex-shrink: 0 !important;
}

.ai-assistant-header h3 {
  margin: 0 !important;
  color: #1e40af !important;
  font-weight: 700 !important;
  font-size: 18px !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.ai-assistant-minimize, .ai-assistant-close {
  width: 32px !important;
  height: 32px !important;
  border-radius: 50% !important;
  background: rgba(255, 255, 255, 0.8) !important;
  border: 1px solid rgba(0, 123, 191, 0.15) !important;
  color: #1e40af !important;
  font-size: 18px !important;
  font-weight: bold !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin-left: 6px !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  cursor: pointer !important;
  box-shadow: 0 2px 8px rgba(0, 123, 191, 0.1) !important;
}

.ai-assistant-minimize:hover, .ai-assistant-close:hover {
  background: rgba(239, 68, 68, 0.1) !important;
  border-color: rgba(239, 68, 68, 0.3) !important;
  color: #dc2626 !important;
  transform: scale(1.1) !important;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2) !important;
}

/* AI助手聊天框 */
.ai-assistant-chatbot {
  background: rgba(255, 255, 255, 0.3) !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  border: 1px solid rgba(0, 123, 191, 0.1) !important;
  border-radius: 12px !important;
  margin: 12px 16px !important;
  flex: 1 !important;
  min-height: 300px !important;
  max-height: 400px !important;
  overflow-y: auto !important;
  box-shadow: inset 0 2px 8px rgba(0, 123, 191, 0.05) !important;
}

.ai-assistant-chatbot .message {
  background: rgba(255, 255, 255, 0.7) !important;
  backdrop-filter: blur(5px) !important;
  -webkit-backdrop-filter: blur(5px) !important;
  border: 1px solid rgba(0, 123, 191, 0.1) !important;
  border-radius: 8px !important;
  margin: 4px 0 !important;
  padding: 8px 12px !important;
}

.ai-assistant-chatbot .message.user {
  background: rgba(0, 123, 191, 0.1) !important;
  border-color: rgba(0, 123, 191, 0.2) !important;
  margin-left: 10% !important;
}

.ai-assistant-chatbot .message.bot {
  background: rgba(0, 200, 150, 0.1) !important;
  border-color: rgba(0, 200, 150, 0.2) !important;
  margin-right: 10% !important;
}

/* AI助手输入框 */
.ai-assistant-input {
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  border: 2px solid rgba(0, 123, 191, 0.15) !important;
  border-radius: 12px !important;
  margin: 12px 16px 8px 16px !important;
  padding: 12px 16px !important;
  font-size: 14px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  resize: none !important;
  min-height: 44px !important;
}

.ai-assistant-input:focus {
  background: rgba(255, 255, 255, 0.95) !important;
  border-color: rgba(0, 123, 191, 0.4) !important;
  box-shadow:
    0 4px 16px rgba(0, 123, 191, 0.12),
    0 0 0 4px rgba(0, 123, 191, 0.08) !important;
  outline: none !important;
  transform: translateY(-1px) !important;
}

/* 快捷问题按钮 */
.quick-questions {
  padding: 12px 16px !important;
  border-bottom: 1px solid rgba(0, 123, 191, 0.08) !important;
  background: rgba(0, 123, 191, 0.02) !important;
  flex-shrink: 0 !important;
}

.quick-questions h4 {
  margin: 0 0 12px 0 !important;
  color: #1e40af !important;
  font-size: 15px !important;
  font-weight: 700 !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
}

.quick-question-btn {
  background: rgba(0, 123, 191, 0.06) !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  border: 1.5px solid rgba(0, 123, 191, 0.12) !important;
  border-radius: 8px !important;
  color: #1e40af !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  padding: 8px 12px !important;
  margin: 3px 2px !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  cursor: pointer !important;
  box-shadow: 0 1px 3px rgba(0, 123, 191, 0.1) !important;
}

.quick-question-btn:hover {
  background: rgba(0, 123, 191, 0.12) !important;
  border-color: rgba(0, 123, 191, 0.3) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(0, 123, 191, 0.2) !important;
  color: #1d4ed8 !important;
}

/* 深色模式适配 */
.dark .ai-assistant-container {
  background: rgba(30, 41, 59, 0.95) !important;
  border-color: rgba(148, 163, 184, 0.3) !important;
}

.dark .ai-assistant-header {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%) !important;
  border-bottom-color: rgba(148, 163, 184, 0.2) !important;
}

.dark .ai-assistant-header h3 {
  color: #93c5fd !important;
}

.dark .ai-assistant-chatbot {
  background: rgba(30, 41, 59, 0.6) !important;
  border-color: rgba(148, 163, 184, 0.2) !important;
}

.dark .ai-assistant-input {
  background: rgba(30, 41, 59, 0.8) !important;
  color: #e2e8f0 !important;
  border-color: rgba(148, 163, 184, 0.3) !important;
}

.dark .quick-question-btn {
  background: rgba(59, 130, 246, 0.1) !important;
  border-color: rgba(59, 130, 246, 0.2) !important;
  color: #93c5fd !important;
}

.dark .quick-questions h4 {
  color: #93c5fd !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-assistant-container {
    bottom: 20px !important;
    right: 10px !important;
    left: 10px !important;
    width: auto !important;
    max-width: none !important;
  }
  
  .ai-assistant-trigger {
    bottom: 20px !important;
    right: 20px !important;
    width: 60px !important;
    height: 60px !important;
    font-size: 24px !important;
  }
  
  .quick-question-btn {
    font-size: 12px !important;
    padding: 6px 10px !important;
    margin: 2px 1px !important;
  }
}

@media (max-height: 700px) {
  .ai-assistant-container {
    max-height: calc(100vh - 120px) !important;
  }
  
  .ai-assistant-chatbot {
    min-height: 200px !important;
    max-height: 250px !important;
  }
}

/* 滚动条美化 */
.ai-assistant-chatbot::-webkit-scrollbar {
  width: 6px;
}

.ai-assistant-chatbot::-webkit-scrollbar-track {
  background: rgba(0, 123, 191, 0.05);
  border-radius: 3px;
}

.ai-assistant-chatbot::-webkit-scrollbar-thumb {
  background: rgba(0, 123, 191, 0.2);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.ai-assistant-chatbot::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 123, 191, 0.3);
}

/* ===== 智能AI助手样式 ===== */

/* 智能助手触发按钮 */
.smart-assistant-trigger {
  position: fixed !important;
  bottom: 20px !important;
  right: 20px !important;
  z-index: 1001 !important;
  width: 120px !important;
  height: 50px !important;
  background: linear-gradient(135deg,
    rgba(16, 185, 129, 0.9) 0%,
    rgba(34, 197, 94, 0.8) 50%,
    rgba(0, 200, 150, 0.9) 100%) !important;
  border: 2px solid rgba(16, 185, 129, 0.3) !important;
  border-radius: 25px !important;
  color: white !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  cursor: pointer !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow:
    0 8px 25px rgba(16, 185, 129, 0.3),
    0 4px 12px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
}

.smart-assistant-trigger:hover {
  transform: translateY(-2px) scale(1.05) !important;
  background: linear-gradient(135deg,
    rgba(16, 185, 129, 1) 0%,
    rgba(34, 197, 94, 0.9) 50%,
    rgba(0, 200, 150, 1) 100%) !important;
  box-shadow:
    0 12px 35px rgba(16, 185, 129, 0.4),
    0 6px 18px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

.smart-assistant-trigger:active {
  transform: translateY(-1px) scale(1.02) !important;
}

/* 智能助手容器 */
.smart-assistant-container {
  position: fixed !important;
  bottom: 80px !important;
  right: 20px !important;
  width: 580px !important;
  max-width: 90vw !important;
  height: 800px !important;
  max-height: 85vh !important;
  background: linear-gradient(145deg,
    rgba(255, 255, 255, 0.98) 0%,
    rgba(248, 250, 252, 0.95) 100%) !important;
  backdrop-filter: blur(25px) !important;
  -webkit-backdrop-filter: blur(25px) !important;
  border: 2px solid transparent !important;
  background-clip: padding-box !important;
  border-radius: 20px !important;
  box-shadow:
    0 25px 60px rgba(0, 123, 191, 0.15),
    0 15px 35px rgba(0, 200, 150, 0.08),
    0 5px 15px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.8),
    inset 0 -1px 0 rgba(0, 123, 191, 0.1) !important;
  z-index: 1000 !important;
  overflow: hidden !important;
  animation: smart-assistant-slide-in 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) !important;
  padding: 0 !important;
  display: flex !important;
  flex-direction: column !important;
}

@keyframes smart-assistant-slide-in {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 智能助手标题栏 */
.smart-assistant-header {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.1) 0%,
    rgba(16, 185, 129, 0.1) 100%) !important;
  padding: 16px 20px !important;
  border-bottom: 1px solid rgba(0, 123, 191, 0.1) !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
}

.smart-assistant-header h3 {
  margin: 0 !important;
  color: #1e293b !important;
  font-size: 18px !important;
  font-weight: 600 !important;
}

.smart-assistant-close {
  width: 32px !important;
  height: 32px !important;
  border-radius: 50% !important;
  background: rgba(239, 68, 68, 0.1) !important;
  border: 1px solid rgba(239, 68, 68, 0.2) !important;
  color: #dc2626 !important;
  font-size: 16px !important;
  font-weight: bold !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.smart-assistant-close:hover {
  background: rgba(239, 68, 68, 0.2) !important;
  transform: scale(1.1) !important;
}

/* 智能助手标签页 */
.smart-assistant-tabs {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

.smart-assistant-tabs .tab-nav {
  background: rgba(248, 250, 252, 0.8) !important;
  border-bottom: 1px solid rgba(0, 123, 191, 0.1) !important;
  padding: 0 20px !important;
}

.smart-assistant-tabs .tab-nav button {
  background: transparent !important;
  border: none !important;
  padding: 12px 16px !important;
  margin: 0 4px !important;
  border-radius: 8px 8px 0 0 !important;
  color: #64748b !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.smart-assistant-tabs .tab-nav button.selected {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.1) 0%,
    rgba(16, 185, 129, 0.1) 100%) !important;
  color: #0f172a !important;
  border-bottom: 2px solid #10b981 !important;
}

/* 聊天界面样式 */
.smart-assistant-chatbot {
  background: transparent !important;
  border: none !important;
  padding: 20px !important;
}

.smart-assistant-chatbot .message {
  margin-bottom: 16px !important;
  padding: 12px 16px !important;
  border-radius: 12px !important;
  max-width: 85% !important;
}

.smart-assistant-chatbot .message.user {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.1) 0%,
    rgba(99, 102, 241, 0.1) 100%) !important;
  margin-left: auto !important;
  border: 1px solid rgba(59, 130, 246, 0.2) !important;
}

.smart-assistant-chatbot .message.bot {
  background: linear-gradient(135deg,
    rgba(16, 185, 129, 0.1) 0%,
    rgba(34, 197, 94, 0.1) 100%) !important;
  margin-right: auto !important;
  border: 1px solid rgba(16, 185, 129, 0.2) !important;
}

/* 快捷问题按钮 */
.quick-questions-row {
  padding: 0 20px !important;
  margin-bottom: 8px !important;
}

.quick-questions-buttons {
  padding: 0 20px !important;
  margin-bottom: 16px !important;
  gap: 8px !important;
  flex-wrap: wrap !important;
}

.quick-question-btn {
  background: linear-gradient(135deg,
    rgba(99, 102, 241, 0.1) 0%,
    rgba(139, 92, 246, 0.1) 100%) !important;
  border: 1px solid rgba(99, 102, 241, 0.2) !important;
  color: #4338ca !important;
  font-size: 12px !important;
  padding: 6px 12px !important;
  border-radius: 16px !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
}

.quick-question-btn:hover {
  background: linear-gradient(135deg,
    rgba(99, 102, 241, 0.2) 0%,
    rgba(139, 92, 246, 0.2) 100%) !important;
  transform: translateY(-1px) !important;
}

/* 输入框样式 */
.smart-assistant-input {
  border: 1px solid rgba(0, 123, 191, 0.2) !important;
  border-radius: 12px !important;
  padding: 12px 16px !important;
  background: rgba(255, 255, 255, 0.8) !important;
  transition: all 0.2s ease !important;
}

.smart-assistant-input:focus {
  border-color: rgba(16, 185, 129, 0.4) !important;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
  background: rgba(255, 255, 255, 0.95) !important;
}

/* 清空对话按钮 */
.clear-chat-btn {
  background: linear-gradient(135deg,
    rgba(239, 68, 68, 0.1) 0%,
    rgba(220, 38, 38, 0.1) 100%) !important;
  border: 1px solid rgba(239, 68, 68, 0.2) !important;
  color: #dc2626 !important;
  margin: 0 20px 20px 20px !important;
  border-radius: 8px !important;
  transition: all 0.2s ease !important;
}

.clear-chat-btn:hover {
  background: linear-gradient(135deg,
    rgba(239, 68, 68, 0.2) 0%,
    rgba(220, 38, 38, 0.2) 100%) !important;
}

/* 配置面板样式 */
.config-content {
  padding: 20px !important;
  gap: 16px !important;
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0.05) 0%,
    rgba(248, 250, 252, 0.02) 100%) !important;
  max-height: none !important;
  overflow: visible !important;
}

.config-textbox {
  margin-bottom: 12px !important;
}

.config-textbox input {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.8) 100%) !important;
  border: 1px solid rgba(0, 123, 191, 0.15) !important;
  border-radius: 8px !important;
  padding: 10px 12px !important;
  font-size: 14px !important;
  transition: all 0.3s ease !important;
}

.config-textbox input:focus {
  border-color: rgba(0, 123, 191, 0.3) !important;
  box-shadow: 0 0 0 3px rgba(0, 123, 191, 0.08) !important;
  background: rgba(255, 255, 255, 0.95) !important;
}

.config-slider {
  margin-bottom: 16px !important;
}

.config-save-btn {
  background: linear-gradient(135deg,
    rgba(16, 185, 129, 0.9) 0%,
    rgba(34, 197, 94, 0.8) 50%,
    rgba(0, 200, 150, 0.9) 100%) !important;
  border: 1px solid rgba(16, 185, 129, 0.2) !important;
  color: white !important;
  font-weight: 600 !important;
  border-radius: 8px !important;
  padding: 10px 16px !important;
  margin-top: 16px !important;
  transition: all 0.3s ease !important;
  cursor: pointer !important;
  box-shadow:
    0 4px 12px rgba(16, 185, 129, 0.2),
    0 2px 6px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

.config-save-btn:hover {
  background: linear-gradient(135deg,
    rgba(16, 185, 129, 1) 0%,
    rgba(34, 197, 94, 0.9) 50%,
    rgba(0, 200, 150, 1) 100%) !important;
  transform: translateY(-1px) scale(1.02) !important;
  box-shadow:
    0 6px 18px rgba(16, 185, 129, 0.3),
    0 3px 9px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

/* 工具面板样式 */
.tools-content {
  padding: 20px !important;
  gap: 16px !important;
}

.tools-input {
  margin-bottom: 12px !important;
}

.tools-btn {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.9) 0%,
    rgba(99, 102, 241, 0.8) 100%) !important;
  border: 1px solid rgba(59, 130, 246, 0.2) !important;
  color: white !important;
  font-weight: 600 !important;
  border-radius: 8px !important;
  padding: 10px 16px !important;
  margin-bottom: 16px !important;
  transition: all 0.3s ease !important;
  cursor: pointer !important;
}

.tools-btn:hover {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 1) 0%,
    rgba(99, 102, 241, 0.9) 100%) !important;
  transform: translateY(-1px) !important;
}

.tools-textarea {
  margin-bottom: 12px !important;
}

.recommendations-output,
.log-analysis-output {
  background: rgba(248, 250, 252, 0.8) !important;
  border: 1px solid rgba(0, 123, 191, 0.1) !important;
  border-radius: 8px !important;
  padding: 16px !important;
  margin-top: 12px !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-assistant-container {
    width: 95vw !important;
    height: 90vh !important;
    bottom: 10px !important;
    right: 2.5vw !important;
  }

  .smart-assistant-trigger {
    bottom: 10px !important;
    right: 10px !important;
    width: 100px !important;
    height: 45px !important;
    font-size: 12px !important;
  }

  .quick-questions-buttons {
    flex-direction: column !important;
  }

  .quick-question-btn {
    width: 100% !important;
    margin-bottom: 4px !important;
  }
}

/* 滚动条样式 */
.smart-assistant-container::-webkit-scrollbar {
  width: 6px;
}

.smart-assistant-container::-webkit-scrollbar-track {
  background: rgba(0, 123, 191, 0.05);
  border-radius: 3px;
}

.smart-assistant-container::-webkit-scrollbar-thumb {
  background: rgba(0, 123, 191, 0.2);
  border-radius: 3px;
}

.smart-assistant-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 123, 191, 0.3);
}

/* Logo响应式设计 - 超大尺寸精致版本 */
@media (max-width: 768px) {
  /* Logo响应式调整 - 移动设备 */
  .centered-logo-image {
    width: 200px !important;
    height: 133px !important;
    margin: 25px auto 35px auto !important;
    box-shadow:
      0 4px 18px rgba(0, 123, 191, 0.35),
      0 8px 35px rgba(0, 123, 191, 0.18),
      0 2px 6px rgba(0, 123, 191, 0.5),
      inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
    border: 1.5px solid rgba(255, 255, 255, 0.25) !important;
  }

  .centered-logo img {
    max-width: 200px !important;
    max-height: 133px !important;
  }

  .logo-container {
    margin: 25px 0 35px 0 !important;
  }

  .logo-container::before {
    width: 250px !important;
    height: 170px !important;
  }

  .centered-logo-image::before {
    top: -8px !important;
    left: -8px !important;
    right: -8px !important;
    bottom: -8px !important;
    border-radius: 18px !important;
  }
}

/* 平板设备响应式 */
@media (max-width: 1024px) and (min-width: 769px) {
  /* Logo响应式调整 - 平板设备 */
  .centered-logo-image {
    width: 280px !important;
    height: 187px !important;
    margin: 30px auto 40px auto !important;
    box-shadow:
      0 5px 22px rgba(0, 123, 191, 0.38),
      0 10px 45px rgba(0, 123, 191, 0.2),
      0 2px 7px rgba(0, 123, 191, 0.55),
      inset 0 1px 0 rgba(255, 255, 255, 0.35) !important;
    border: 1.8px solid rgba(255, 255, 255, 0.28) !important;
  }

  .centered-logo img {
    max-width: 280px !important;
    max-height: 187px !important;
  }

  .logo-container {
    margin: 30px 0 40px 0 !important;
  }

  .logo-container::before {
    width: 330px !important;
    height: 220px !important;
  }

  .centered-logo-image::before {
    top: -12px !important;
    left: -12px !important;
    right: -12px !important;
    bottom: -12px !important;
    border-radius: 22px !important;
  }
}

/* 大屏幕设备优化 */
@media (min-width: 1440px) {
  /* Logo响应式调整 - 大屏幕 */
  .centered-logo-image {
    width: 420px !important;
    height: 280px !important;
    margin: 40px auto 50px auto !important;
    box-shadow:
      0 7px 30px rgba(0, 123, 191, 0.45),
      0 15px 60px rgba(0, 123, 191, 0.25),
      0 3px 10px rgba(0, 123, 191, 0.65),
      inset 0 1px 0 rgba(255, 255, 255, 0.45) !important;
    border: 2.5px solid rgba(255, 255, 255, 0.35) !important;
  }

  .centered-logo img {
    max-width: 420px !important;
    max-height: 280px !important;
  }

  .logo-container {
    margin: 40px 0 50px 0 !important;
  }

  .logo-container::before {
    width: 480px !important;
    height: 320px !important;
  }

  .centered-logo-image::before {
    top: -18px !important;
    left: -18px !important;
    right: -18px !important;
    bottom: -18px !important;
    border-radius: 28px !important;
  }
}

/* 超大屏幕设备优化 */
@media (min-width: 1920px) {
  /* Logo响应式调整 - 超大屏幕 */
  .centered-logo-image {
    width: 480px !important;
    height: 320px !important;
    margin: 45px auto 55px auto !important;
    box-shadow:
      0 8px 35px rgba(0, 123, 191, 0.5),
      0 18px 70px rgba(0, 123, 191, 0.3),
      0 4px 12px rgba(0, 123, 191, 0.7),
      inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
    border: 3px solid rgba(255, 255, 255, 0.4) !important;
  }

  .centered-logo img {
    max-width: 480px !important;
    max-height: 320px !important;
  }

  .logo-container {
    margin: 45px 0 55px 0 !important;
  }

  .logo-container::before {
    width: 540px !important;
    height: 360px !important;
  }

  .centered-logo-image::before {
    top: -20px !important;
    left: -20px !important;
    right: -20px !important;
    bottom: -20px !important;
    border-radius: 30px !important;
  }
}
"""
