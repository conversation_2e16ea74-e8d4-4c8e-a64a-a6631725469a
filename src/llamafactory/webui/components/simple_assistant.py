#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版智能AI助手组件
解决Gradio兼容性问题
"""

import json
import os
from typing import Dict, Any, List, Tuple

from ...extras.packages import is_gradio_available

if is_gradio_available():
    import gradio as gr

# 配置文件路径
CONFIG_FILE = "simple_assistant_config.json"

# AI服务商配置
AI_PROVIDERS = {
    "OpenAI": {
        "models": [
            "gpt-4", "gpt-4-turbo", "gpt-4o", "gpt-4o-mini",
            "gpt-3.5-turbo", "gpt-3.5-turbo-16k"
        ],
        "api_base": "https://api.openai.com/v1",
        "headers": {"Authorization": "Bearer {api_key}"},
        "description": "OpenAI官方API服务",
        "test_model": "gpt-3.5-turbo"
    },
    "Claude": {
        "models": [
            "claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022",
            "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"
        ],
        "api_base": "https://api.anthropic.com/v1",
        "headers": {"x-api-key": "{api_key}", "anthropic-version": "2023-06-01"},
        "description": "Anthropic Claude API服务",
        "test_model": "claude-3-5-haiku-20241022"
    },
    "Google Gemini": {
        "models": [
            "gemini-pro", "gemini-pro-vision", "gemini-1.5-pro",
            "gemini-1.5-flash", "gemini-1.0-pro"
        ],
        "api_base": "https://generativelanguage.googleapis.com/v1beta",
        "headers": {"Authorization": "Bearer {api_key}"},
        "description": "Google Gemini API服务",
        "test_model": "gemini-pro"
    },
    "xAI": {
        "models": ["grok-beta", "grok-vision-beta"],
        "api_base": "https://api.x.ai/v1",
        "headers": {"Authorization": "Bearer {api_key}"},
        "description": "xAI Grok API服务",
        "test_model": "grok-beta"
    },
    "阿里云Qwen": {
        "models": [
            "qwen-turbo", "qwen-plus", "qwen-max", "qwen-max-longcontext",
            "qwen-7b-chat", "qwen-14b-chat", "qwen-72b-chat"
        ],
        "api_base": "https://dashscope.aliyuncs.com/api/v1",
        "headers": {"Authorization": "Bearer {api_key}"},
        "description": "阿里云通义千问API服务",
        "test_model": "qwen-turbo"
    },
    "智谱AI": {
        "models": [
            "glm-4", "glm-4-air", "glm-4-flash", "glm-4v",
            "glm-3-turbo", "chatglm3-6b"
        ],
        "api_base": "https://open.bigmodel.cn/api/paas/v4",
        "headers": {"Authorization": "Bearer {api_key}"},
        "description": "智谱AI GLM API服务",
        "test_model": "glm-4-air"
    },
    "百度文心": {
        "models": [
            "ernie-4.0-8k", "ernie-3.5-8k", "ernie-lite-8k",
            "ernie-bot", "ernie-bot-turbo", "ernie-bot-4"
        ],
        "api_base": "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop",
        "headers": {"Authorization": "Bearer {api_key}"},
        "description": "百度文心一言API服务",
        "test_model": "ernie-3.5-8k"
    },
    "讯飞星火": {
        "models": [
            "spark-3.5", "spark-pro", "spark-lite",
            "spark-max", "spark-v3.1", "spark-v2.1"
        ],
        "api_base": "https://spark-api.xf-yun.com/v1",
        "headers": {"Authorization": "Bearer {api_key}"},
        "description": "讯飞星火认知大模型API服务",
        "test_model": "spark-lite"
    },
    "OpenRouter": {
        "models": [
            "gpt-4", "claude-3-5-sonnet", "llama-2-70b-chat", "mistral-7b-instruct",
            "anthropic/claude-3.5-sonnet", "openai/gpt-4-turbo", "meta-llama/llama-2-70b-chat",
            "mistralai/mistral-7b-instruct", "google/gemini-pro", "cohere/command-r-plus"
        ],
        "api_base": "https://openrouter.ai/api/v1",
        "headers": {"Authorization": "Bearer {api_key}"},
        "description": "OpenRouter统一AI模型API服务",
        "test_model": "gpt-4"
    },
    "Poe": {
        "models": [
            "Claude-Sonnet-4", "Grok-4", "GPT-4-Turbo", "Claude-Haiku",
            "Claude-3-Opus", "GPT-4o", "Gemini-Pro", "Claude-3-Sonnet"
        ],
        "api_base": "https://api.poe.com/v1",
        "headers": {"Authorization": "Bearer {api_key}"},
        "description": "Poe AI聊天平台API服务",
        "test_model": "Claude-Haiku"
    },
    "本地模型": {
        "models": [
            "custom-local-model", "llama-2-7b", "llama-2-13b",
            "chatglm-6b", "baichuan-7b", "qwen-7b"
        ],
        "api_base": "http://localhost:8000/v1",
        "headers": {"Authorization": "Bearer {api_key}"},
        "description": "本地部署的模型API服务",
        "test_model": "custom-local-model"
    }
}

# 默认配置
DEFAULT_CONFIG = {
    "selected_provider": "OpenAI",
    "model_id": "gpt-3.5-turbo",  # 新的统一模型ID字段
    "selected_model": "gpt-3.5-turbo",  # 保留兼容性
    "custom_api_base": "",
    "custom_model_name": "",  # 保留兼容性
    "api_keys": {},
    "temperature": 0.7,
    "max_tokens": 400000,
    "timeout": 30
}

# 系统提示词
SYSTEM_PROMPT = """你是Magic Lab的专业AI助手，专门帮助用户进行大语言模型微调。

你的专长包括：
1. 🎯 模型微调参数配置建议
2. 🔧 LoRA、QLoRA等技术指导  
3. 💾 硬件配置和显存优化
4. 📊 训练数据准备和格式化
5. 🚨 故障排除和问题诊断

请用专业、友好的语气回答用户问题，提供具体可行的建议。
"""

# 快捷问题
QUICK_QUESTIONS = [
    "🎯 如何选择合适的学习率？",
    "🔧 LoRA参数推荐设置？", 
    "💾 如何处理显存不足？",
    "📊 训练数据集如何准备？",
    "⚡ QLoRA和LoRA有什么区别？",
    "🎨 如何提高模型生成质量？"
]

def load_config() -> Dict[str, Any]:
    """加载配置"""
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 合并默认配置
                for key, value in DEFAULT_CONFIG.items():
                    if key not in config:
                        config[key] = value
                return config
    except Exception:
        pass
    return DEFAULT_CONFIG.copy()

def save_config(config: Dict[str, Any]) -> None:
    """保存配置"""
    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    except Exception:
        pass

def validate_url(url: str) -> Tuple[bool, str]:
    """验证URL格式"""
    if not url.strip():
        return True, ""  # 空URL是允许的

    import re
    url_pattern = re.compile(
        r'^https?://'  # http:// 或 https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # 域名
        r'localhost|'  # localhost
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # IP地址
        r'(?::\d+)?'  # 可选端口
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)

    if url_pattern.match(url.strip()):
        return True, "✅ URL格式正确"
    else:
        return False, "❌ URL格式无效，请输入有效的HTTP/HTTPS地址"

def validate_api_key(api_key: str, provider: str) -> Tuple[bool, str]:
    """验证API密钥格式"""
    if not api_key.strip():
        return False, "❌ API密钥不能为空"

    key = api_key.strip()

    # 基本长度检查
    if len(key) < 10:
        return False, "❌ API密钥长度过短"

    # 特定服务商的格式检查
    if provider == "OpenAI":
        if not key.startswith("sk-"):
            return False, "❌ OpenAI API密钥应以'sk-'开头"
    elif provider == "Claude":
        if not key.startswith("sk-ant-"):
            return False, "❌ Claude API密钥应以'sk-ant-'开头"
    elif provider == "智谱AI":
        if "." not in key:
            return False, "❌ 智谱AI API密钥格式应包含'.'分隔符"
    elif provider == "OpenRouter":
        if not key.startswith("sk-or-"):
            return False, "❌ OpenRouter API密钥应以'sk-or-'开头"
    elif provider == "Poe":
        # Poe API密钥通常是标准格式，长度检查即可
        if len(key) < 20:
            return False, "❌ Poe API密钥长度不足"

    return True, "✅ API密钥格式正确"

def get_provider_info(provider: str) -> Dict[str, Any]:
    """获取服务商信息"""
    if provider in AI_PROVIDERS:
        return AI_PROVIDERS[provider]
    return {
        "models": [],
        "api_base": "",
        "headers": {},
        "description": "未知服务商",
        "test_model": ""
    }



def call_ai_api(messages: List[Dict], config: Dict[str, Any], test_mode: bool = False) -> str:
    """调用AI API"""
    try:
        import requests

        provider = config.get("selected_provider", "OpenAI")
        # 优先使用用户输入的模型ID
        model = config.get("model_id") or config.get("custom_model_name") or config.get("selected_model", "gpt-3.5-turbo")
        api_keys = config.get("api_keys", {})

        # 获取API配置
        provider_config = get_provider_info(provider)
        if not provider_config["models"]:
            return "❌ 不支持的AI服务商"

        # 获取API基础URL
        api_base = config.get("custom_api_base", "").strip()
        if not api_base:
            api_base = provider_config["api_base"]

        # 验证URL
        url_valid, url_msg = validate_url(api_base)
        if not url_valid:
            return f"❌ API端点URL无效: {url_msg}"

        # 获取API密钥
        api_key = api_keys.get(provider, "")
        if not api_key:
            return f"❌ 请先为{provider}配置API密钥"

        # 验证API密钥
        key_valid, key_msg = validate_api_key(api_key, provider)
        if not key_valid:
            return f"❌ API密钥验证失败: {key_msg}"

        # 构建请求头
        headers = {"Content-Type": "application/json"}
        for key, value in provider_config["headers"].items():
            headers[key] = value.format(api_key=api_key)

        # 构建请求数据
        if provider in ["OpenAI", "本地模型", "xAI", "OpenRouter", "Poe"]:
            url = f"{api_base.rstrip('/')}/chat/completions"
            data = {
                "model": model,
                "messages": messages,
                "temperature": config.get("temperature", 0.7),
                "max_tokens": config.get("max_tokens", 400000)
            }

            # OpenRouter特殊配置
            if provider == "OpenRouter":
                # 添加OpenRouter特有的参数
                data.update({
                    "route": "fallback",  # 启用模型回退
                    "transforms": ["middle-out"]  # 优化响应
                })

            # Poe特殊配置
            elif provider == "Poe":
                # Poe API与OpenAI兼容，使用标准格式即可
                pass
        elif provider == "Claude":
            url = f"{api_base.rstrip('/')}/messages"
            # Claude API格式转换
            system_msg = ""
            user_messages = []
            for msg in messages:
                if msg["role"] == "system":
                    system_msg = msg["content"]
                else:
                    user_messages.append(msg)

            data = {
                "model": model,
                "messages": user_messages,
                "max_tokens": config.get("max_tokens", 400000),
                "temperature": config.get("temperature", 0.7)
            }
            if system_msg:
                data["system"] = system_msg
        elif provider == "Google Gemini":
            url = f"{api_base.rstrip('/')}/models/{model}:generateContent"
            # Gemini API格式转换
            contents = []
            for msg in messages:
                if msg["role"] != "system":
                    contents.append({
                        "role": "user" if msg["role"] == "user" else "model",
                        "parts": [{"text": msg["content"]}]
                    })

            data = {
                "contents": contents,
                "generationConfig": {
                    "temperature": config.get("temperature", 0.7),
                    "maxOutputTokens": config.get("max_tokens", 400000)
                }
            }
        else:
            # 其他服务商使用通用格式
            url = f"{api_base.rstrip('/')}/chat/completions"
            data = {
                "model": model,
                "messages": messages,
                "temperature": config.get("temperature", 0.7),
                "max_tokens": config.get("max_tokens", 400000)
            }

        # 发送请求
        timeout = config.get("timeout", 30)
        response = requests.post(url, headers=headers, json=data, timeout=timeout)

        # 处理响应
        if response.status_code == 200:
            result = response.json()

            if provider == "Claude":
                content = result.get("content", [])
                if content and len(content) > 0:
                    return content[0].get("text", "无响应内容")
                return "无响应内容"
            elif provider == "Google Gemini":
                candidates = result.get("candidates", [])
                if candidates and len(candidates) > 0:
                    content = candidates[0].get("content", {})
                    parts = content.get("parts", [])
                    if parts and len(parts) > 0:
                        return parts[0].get("text", "无响应内容")
                return "无响应内容"
            else:
                # OpenAI兼容格式 (OpenAI, OpenRouter, Poe, 本地模型, xAI等)
                choices = result.get("choices", [])
                if choices and len(choices) > 0:
                    message = choices[0].get("message", {})
                    content = message.get("content", "无响应内容")

                    # 特殊处理某些服务商的响应格式
                    if provider == "OpenRouter":
                        # OpenRouter可能在响应中包含额外信息
                        if isinstance(content, dict):
                            content = content.get("text", str(content))
                    elif provider == "Poe":
                        # Poe响应格式与OpenAI完全兼容
                        pass

                    return content
                return "无响应内容"
        else:
            error_detail = ""
            try:
                error_json = response.json()
                error_detail = error_json.get("error", {}).get("message", response.text[:200])
            except:
                error_detail = response.text[:200]

            return f"❌ API调用失败 ({response.status_code}): {error_detail}"

    except requests.exceptions.Timeout:
        return f"❌ 请求超时，请检查网络连接或增加超时时间"
    except requests.exceptions.ConnectionError:
        return f"❌ 连接失败，请检查API端点地址和网络连接"
    except Exception as e:
        return f"❌ API调用异常: {str(e)}"

def get_hardware_recommendations(gpu_memory: int) -> str:
    """获取硬件配置推荐"""
    if gpu_memory <= 4:
        return """## 🎯 4GB显存配置建议

### 📋 推荐参数
```yaml
quantization_bit: 4
lora_rank: 8
per_device_train_batch_size: 1
gradient_accumulation_steps: 16
cutoff_len: 512
learning_rate: 5e-4
```

### 💡 优化建议
- 使用4位量化减少显存占用
- 小批次训练配合梯度累积
- 较短的序列长度
"""
    elif gpu_memory <= 8:
        return """## 🎯 8GB显存配置建议

### 📋 推荐参数
```yaml
quantization_bit: 4
lora_rank: 16
per_device_train_batch_size: 2
gradient_accumulation_steps: 8
cutoff_len: 1024
learning_rate: 1e-4
```

### 💡 优化建议
- 平衡的量化和批次设置
- 适中的LoRA rank
- 标准序列长度
"""
    elif gpu_memory <= 16:
        return """## 🎯 16GB显存配置建议

### 📋 推荐参数
```yaml
lora_rank: 32
per_device_train_batch_size: 4
gradient_accumulation_steps: 4
cutoff_len: 2048
learning_rate: 1e-4
```

### 💡 优化建议
- 可以不使用量化
- 较大的LoRA rank
- 更长的序列长度
"""
    else:
        return """## 🎯 24GB+显存配置建议

### 📋 推荐参数
```yaml
lora_rank: 64
per_device_train_batch_size: 8
gradient_accumulation_steps: 2
cutoff_len: 4096
learning_rate: 5e-5
```

### 💡 优化建议
- 高质量训练配置
- 大LoRA rank获得更好效果
- 支持长序列训练
"""

def analyze_training_log(log_content: str) -> str:
    """分析训练日志"""
    log_lower = log_content.lower()
    
    if "cuda out of memory" in log_lower or "out of memory" in log_lower:
        return """## 🚨 显存不足问题

### 🔧 解决方案
1. **减少批次大小**：`per_device_train_batch_size: 1`
2. **增加梯度累积**：`gradient_accumulation_steps: 16`
3. **启用量化**：`quantization_bit: 4`
4. **启用梯度检查点**：`gradient_checkpointing: true`
5. **减少序列长度**：`cutoff_len: 512`

### 💡 其他建议
- 关闭不必要的程序释放显存
- 考虑使用更小的模型
"""
    
    elif "nan" in log_lower:
        return """## ⚠️ NaN值问题

### 🔧 解决方案
1. **降低学习率**：`learning_rate: 1e-5`
2. **启用梯度裁剪**：`max_grad_norm: 1.0`
3. **检查数据质量**：确保训练数据没有异常值
4. **使用更稳定的优化器**：`optim: adamw_torch`

### 💡 预防措施
- 监控梯度范数
- 使用学习率调度器
"""
    
    elif "loss" in log_lower and any(word in log_lower for word in ["step", "epoch"]):
        return """## 📈 训练进度正常

### 📊 监控建议
- 观察损失下降趋势
- 检查验证集性能
- 监控学习率变化
- 注意过拟合迹象

### 🎯 优化建议
- 如果损失不下降，考虑调整学习率
- 如果损失震荡，可能需要降低学习率
- 定期保存检查点
"""
    
    else:
        return """## 📋 日志分析

请提供更详细的训练日志信息，包括：
- 错误信息
- 损失值变化
- 训练步数
- 显存使用情况

这样我可以提供更准确的分析和建议。
"""

def create_simple_assistant() -> Dict[str, "gr.Component"]:
    """创建简化版智能助手"""

    # 加载配置
    config = load_config()

    # 直接在布局中创建组件，避免重复渲染
    gr.HTML("<h2 style='text-align: center; color: #1f2937;'>🤖 Magic Lab智能助手</h2>")

    with gr.Tabs():
        # 对话标签页
        with gr.Tab("💬 智能对话"):
            chatbot = gr.Chatbot(
                height=400,
                show_copy_button=True,
                type="tuples"
            )

            # 快捷问题
            gr.HTML("<p><strong>🚀 快捷问题：</strong></p>")
            with gr.Row():
                quick_btn1 = gr.Button("🎯 如何选择合适的学习率？", size="sm")
                quick_btn2 = gr.Button("🔧 LoRA参数推荐设置？", size="sm")

            with gr.Row():
                quick_btn3 = gr.Button("💾 如何处理显存不足？", size="sm")
                quick_btn4 = gr.Button("📊 训练数据集如何准备？", size="sm")

            # 输入区域
            with gr.Row():
                msg_input = gr.Textbox(
                    placeholder="请输入您的问题...",
                    container=False,
                    scale=4
                )
                send_btn = gr.Button("发送", variant="primary", scale=1)

            clear_btn = gr.Button("🗑️ 清空对话")

        # AI模型配置标签页
        with gr.Tab("⚙️ AI模型配置"):
            gr.HTML("<h3>🤖 AI模型配置</h3>")

            # 服务商选择和信息显示
            with gr.Row():
                provider_dropdown = gr.Dropdown(
                    choices=list(AI_PROVIDERS.keys()),
                    value=config.get("selected_provider", "OpenAI"),
                    label="AI服务商",
                    scale=2,
                    info="选择您要使用的AI服务商"
                )

            provider_info = gr.HTML(
                value=f"<div style='padding: 10px; background: #f0f0f0; border-radius: 5px;'>"
                      f"<strong>{AI_PROVIDERS[config.get('selected_provider', 'OpenAI')]['description']}</strong></div>"
            )

            # 模型配置 - 改进为完全自定义
            gr.HTML("<h4>🤖 模型配置</h4>")

            # 主要模型ID输入
            with gr.Row():
                model_id_input = gr.Textbox(
                    label="模型ID",
                    placeholder="输入完整的模型ID，如：gpt-4, claude-3-5-sonnet-20241022, glm-4-air",
                    value=config.get("custom_model_name") or config.get("selected_model", "gpt-3.5-turbo"),
                    scale=3,
                    info="支持任意模型ID，完全自定义"
                )
                model_validate_btn = gr.Button("🔍 验证模型", variant="secondary", scale=1)

            # 模型验证状态
            model_status = gr.HTML()

            # 常用模型快速选择
            with gr.Accordion("📋 常用模型快速选择", open=False):
                gr.HTML("""
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;">
                    <p><strong>💡 点击下方按钮快速填入常用模型ID：</strong></p>
                </div>
                """)

                # OpenAI模型
                with gr.Row():
                    gr.HTML("<strong>OpenAI:</strong>")
                    gpt4_btn = gr.Button("gpt-4", size="sm")
                    gpt4o_btn = gr.Button("gpt-4o", size="sm")
                    gpt35_btn = gr.Button("gpt-3.5-turbo", size="sm")
                    gpt4o_mini_btn = gr.Button("gpt-4o-mini", size="sm")

                # Claude模型
                with gr.Row():
                    gr.HTML("<strong>Claude:</strong>")
                    claude_sonnet_btn = gr.Button("claude-3-5-sonnet-20241022", size="sm")
                    claude_haiku_btn = gr.Button("claude-3-5-haiku-20241022", size="sm")
                    claude_opus_btn = gr.Button("claude-3-opus-20240229", size="sm")

                # 智谱AI模型
                with gr.Row():
                    gr.HTML("<strong>智谱AI:</strong>")
                    glm4_btn = gr.Button("glm-4", size="sm")
                    glm4_air_btn = gr.Button("glm-4-air", size="sm")
                    glm4_flash_btn = gr.Button("glm-4-flash", size="sm")
                    glm4v_btn = gr.Button("glm-4v", size="sm")

                # OpenRouter模型
                with gr.Row():
                    gr.HTML("<strong>OpenRouter:</strong>")
                    or_gpt4_btn = gr.Button("gpt-4", size="sm")
                    or_claude_btn = gr.Button("claude-3-5-sonnet", size="sm")
                    or_llama_btn = gr.Button("llama-2-70b-chat", size="sm")
                    or_mistral_btn = gr.Button("mistral-7b-instruct", size="sm")

                # Poe模型
                with gr.Row():
                    gr.HTML("<strong>Poe:</strong>")
                    poe_claude_btn = gr.Button("Claude-Sonnet-4", size="sm")
                    poe_grok_btn = gr.Button("Grok-4", size="sm")
                    poe_gpt4_btn = gr.Button("GPT-4-Turbo", size="sm")
                    poe_haiku_btn = gr.Button("Claude-Haiku", size="sm")

                # 其他模型
                with gr.Row():
                    gr.HTML("<strong>其他:</strong>")
                    qwen_turbo_btn = gr.Button("qwen-turbo", size="sm")
                    qwen_plus_btn = gr.Button("qwen-plus", size="sm")
                    gemini_pro_btn = gr.Button("gemini-pro", size="sm")
                    gemini_flash_btn = gr.Button("gemini-1.5-flash", size="sm")

                # 本地模型示例
                with gr.Row():
                    gr.HTML("<strong>本地模型示例:</strong>")
                    llama2_btn = gr.Button("llama-2-7b-chat", size="sm")
                    chatglm_btn = gr.Button("chatglm3-6b", size="sm")
                    baichuan_btn = gr.Button("baichuan2-7b-chat", size="sm")
                    custom_btn = gr.Button("my-custom-model", size="sm")

            # API配置
            gr.HTML("<h4>🔑 API配置</h4>")
            with gr.Row():
                api_key_input = gr.Textbox(
                    label="API密钥",
                    type="password",
                    placeholder="请输入API密钥",
                    scale=3,
                    info="您的API密钥将被安全存储"
                )
                api_key_status = gr.HTML()

            with gr.Row():
                save_key_btn = gr.Button("💾 保存密钥", variant="secondary", scale=1)
                validate_key_btn = gr.Button("🔍 验证密钥", variant="secondary", scale=1)
                clear_key_btn = gr.Button("🗑️ 清除密钥", variant="secondary", scale=1)

            # 自定义API端点配置
            gr.HTML("<h4>🔧 API端点配置</h4>")
            with gr.Row():
                custom_api_base = gr.Textbox(
                    label="自定义API端点URL",
                    placeholder="留空使用默认端点，如：https://api.openai.com/v1",
                    value=config.get("custom_api_base", ""),
                    scale=3,
                    info="支持自定义API服务器地址"
                )
                url_status = gr.HTML()

            # 显示当前使用的API端点
            current_endpoint = gr.HTML(
                value=f"<div style='padding: 8px; background: #e8f4fd; border-radius: 5px; font-family: monospace;'>"
                      f"<strong>当前端点:</strong> {config.get('custom_api_base') or AI_PROVIDERS[config.get('selected_provider', 'OpenAI')]['api_base']}</div>"
            )

            # 参数配置
            gr.HTML("<h4>📊 生成参数配置</h4>")
            with gr.Row():
                temperature_slider = gr.Slider(
                    minimum=0.0,
                    maximum=2.0,
                    value=config.get("temperature", 0.7),
                    step=0.1,
                    label="温度参数",
                    scale=1,
                    info="控制输出的随机性，0.0-2.0"
                )
                max_tokens_slider = gr.Slider(
                    minimum=100,
                    maximum=400000,
                    value=config.get("max_tokens", 400000),
                    step=1000,
                    label="最大Token数",
                    scale=1,
                    info="限制生成内容的长度，最大40万token"
                )
                timeout_slider = gr.Slider(
                    minimum=10,
                    maximum=120,
                    value=config.get("timeout", 30),
                    step=5,
                    label="请求超时(秒)",
                    scale=1,
                    info="API请求的超时时间"
                )

            # 配置状态显示
            config_status = gr.HTML()

            # 配置操作按钮
            gr.HTML("<h4>🛠️ 配置操作</h4>")
            with gr.Row():
                save_config_btn = gr.Button("💾 保存完整配置", variant="primary", scale=2)
                test_connection_btn = gr.Button("🔗 测试API连接", variant="secondary", scale=2)

            with gr.Row():
                load_template_btn = gr.Button("📋 加载模板配置", variant="secondary", scale=2)
                reset_config_btn = gr.Button("🔄 重置为默认", variant="secondary", scale=2)

            # 本地模型专用配置
            gr.HTML("<h4>🏠 本地模型配置</h4>")
            with gr.Accordion("本地模型设置", open=False):
                gr.HTML("""
                <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0;">
                    <p><strong>💡 本地模型配置说明：</strong></p>
                    <ul>
                        <li><strong>API端点：</strong> 通常为 http://localhost:8000/v1 或 http://127.0.0.1:8000/v1</li>
                        <li><strong>模型ID：</strong> 可以是任意字符串，如 'llama-2-7b', 'chatglm-6b' 等</li>
                        <li><strong>API密钥：</strong> 本地模型通常不需要真实密钥，可填入 'local-key'</li>
                        <li><strong>测试连接：</strong> 确保本地API服务器正在运行</li>
                    </ul>
                </div>
                """)

                with gr.Row():
                    local_endpoint_input = gr.Textbox(
                        label="本地API端点",
                        value="http://localhost:8000/v1",
                        placeholder="http://localhost:8000/v1"
                    )
                    local_model_input = gr.Textbox(
                        label="本地模型ID",
                        value="custom-local-model",
                        placeholder="llama-2-7b"
                    )

                apply_local_config_btn = gr.Button("🔧 应用本地模型配置", variant="secondary")

            # 配置模板和帮助信息
            gr.HTML("<h4>📋 支持的AI服务商</h4>")
            with gr.Accordion("查看所有支持的服务商和模型", open=False):
                providers_info = ""
                for provider, info in AI_PROVIDERS.items():
                    models_str = ", ".join(info["models"][:3]) + ("..." if len(info["models"]) > 3 else "")
                    providers_info += f"""
                    <div style="margin: 10px 0; padding: 10px; border-left: 4px solid #007bff; background: #f8f9fa;">
                        <strong>{provider}</strong> - {info['description']}<br>
                        <small>模型: {models_str}</small><br>
                        <small>端点: {info['api_base']}</small>
                    </div>
                    """

                gr.HTML(providers_info)

        # 工具标签页
        with gr.Tab("🛠️ 训练工具"):
            gr.HTML("<h4>🔧 硬件配置推荐</h4>")

            gpu_memory_input = gr.Slider(
                label="GPU显存大小 (GB)",
                value=8,
                minimum=1,
                maximum=80,
                step=1
            )

            get_recommendations_btn = gr.Button("获取推荐配置")
            recommendations_output = gr.Markdown()

            gr.HTML("<h4>📊 训练日志分析</h4>")

            log_input = gr.Textbox(
                label="粘贴训练日志",
                lines=5,
                placeholder="请粘贴训练日志内容..."
            )

            analyze_log_btn = gr.Button("分析日志")
            log_analysis_output = gr.Markdown()

    # 事件处理函数
    def smart_chat(message, history, config_state):
        """智能聊天功能"""
        if not message.strip():
            return history, ""

        # 准备消息格式
        messages = [{"role": "system", "content": SYSTEM_PROMPT}]

        # 添加历史对话
        for msg in history[-5:]:  # 只保留最近5轮对话
            if len(msg) == 2:
                messages.append({"role": "user", "content": msg[0]})
                messages.append({"role": "assistant", "content": msg[1]})

        # 添加当前消息
        messages.append({"role": "user", "content": message})

        # 调用AI API
        response = call_ai_api(messages, config_state)

        history.append([message, response])
        return history, ""

    def update_provider_info(provider):
        """更新服务商信息"""
        if provider in AI_PROVIDERS:
            provider_config = AI_PROVIDERS[provider]
            info_html = f"""
            <div style='padding: 10px; background: #f0f0f0; border-radius: 5px;'>
                <strong>{provider_config['description']}</strong><br>
                <small>默认端点: {provider_config['api_base']}</small>
            </div>
            """
            endpoint_html = f"""
            <div style='padding: 8px; background: #e8f4fd; border-radius: 5px; font-family: monospace;'>
                <strong>当前端点:</strong> {provider_config['api_base']}
            </div>
            """
            return info_html, endpoint_html
        return "", ""

    def validate_model_id(model_id, provider):
        """验证模型ID"""
        if not model_id.strip():
            return "<span style='color: red;'>❌ 模型ID不能为空</span>"

        model = model_id.strip()

        # 基本格式检查
        if len(model) < 3:
            return "<span style='color: red;'>❌ 模型ID过短</span>"

        # 特定服务商的模型ID建议
        suggestions = {
            "OpenAI": ["gpt-4", "gpt-4o", "gpt-3.5-turbo", "gpt-4-turbo"],
            "Claude": ["claude-3-5-sonnet", "claude-3-5-haiku", "claude-3-opus"],
            "智谱AI": ["glm-4", "glm-4-air", "glm-4-flash", "glm-4v"],
            "Google Gemini": ["gemini-pro", "gemini-1.5-pro", "gemini-1.5-flash"],
            "阿里云Qwen": ["qwen-turbo", "qwen-plus", "qwen-max"],
            "OpenRouter": ["gpt-4", "claude-3-5-sonnet", "llama-2-70b-chat", "mistral-7b-instruct"],
            "Poe": ["Claude-Sonnet-4", "Grok-4", "GPT-4-Turbo", "Claude-Haiku"],
            "本地模型": ["llama-2-7b", "chatglm3-6b", "baichuan2-7b"]
        }

        if provider in suggestions:
            common_models = suggestions[provider]
            # 检查是否是常见模型
            for common in common_models:
                if common in model.lower():
                    return f"<span style='color: green;'>✅ 识别为{provider}模型</span>"

        # 通用验证
        model_keywords = [
            "gpt", "claude", "glm", "qwen", "gemini", "llama", "chatglm", "baichuan",
            "mistral", "grok", "command", "cohere", "anthropic", "openai", "meta-llama"
        ]

        if any(keyword in model.lower() for keyword in model_keywords):
            return "<span style='color: green;'>✅ 模型ID格式正确</span>"
        elif model.lower().startswith(("claude-", "gpt-", "glm-", "qwen-", "gemini-")):
            return "<span style='color: green;'>✅ 识别为标准模型格式</span>"
        else:
            return "<span style='color: orange;'>⚠️ 自定义模型ID，请确保正确</span>"

    def set_model_id(model_name):
        """设置模型ID"""
        return model_name

    def validate_api_key_input(provider, api_key):
        """验证API密钥输入"""
        if not api_key.strip():
            return "<span style='color: gray;'>请输入API密钥</span>"

        is_valid, message = validate_api_key(api_key, provider)
        color = "green" if is_valid else "red"
        return f"<span style='color: {color};'>{message}</span>"

    def validate_url_input(url):
        """验证URL输入"""
        if not url.strip():
            return "<span style='color: gray;'>使用默认端点</span>"

        is_valid, message = validate_url(url)
        color = "green" if is_valid else "red"
        return f"<span style='color: {color};'>{message}</span>"

    def save_api_key(provider, api_key, config_state):
        """保存API密钥"""
        if not api_key.strip():
            return config_state, "<span style='color: red;'>❌ API密钥不能为空</span>"

        # 验证API密钥格式
        is_valid, message = validate_api_key(api_key, provider)
        if not is_valid:
            return config_state, f"<span style='color: red;'>{message}</span>"

        if "api_keys" not in config_state:
            config_state["api_keys"] = {}

        config_state["api_keys"][provider] = api_key.strip()
        save_config(config_state)

        return config_state, f"<span style='color: green;'>✅ {provider} API密钥已保存并验证</span>"

    def clear_api_key(provider, config_state):
        """清除API密钥"""
        if "api_keys" in config_state and provider in config_state["api_keys"]:
            del config_state["api_keys"][provider]
            save_config(config_state)
            return config_state, f"<span style='color: orange;'>🗑️ {provider} API密钥已清除</span>", ""
        return config_state, f"<span style='color: gray;'>该服务商没有保存的API密钥</span>", ""

    def save_full_config(provider, model_id, custom_api_base, temperature, max_tokens, timeout, config_state):
        """保存完整配置"""
        # 验证模型ID
        if not model_id.strip():
            return config_state, "<span style='color: red;'>❌ 模型ID不能为空</span>"

        # 验证自定义API端点
        if custom_api_base.strip():
            is_valid, message = validate_url(custom_api_base.strip())
            if not is_valid:
                return config_state, f"<span style='color: red;'>❌ {message}</span>"

        config_state.update({
            "selected_provider": provider,
            "model_id": model_id.strip(),  # 新的模型ID字段
            "custom_api_base": custom_api_base.strip(),
            "temperature": temperature,
            "max_tokens": int(max_tokens),
            "timeout": int(timeout)
        })

        save_config(config_state)
        return config_state, f"<span style='color: green;'>✅ 配置已保存 (模型: {model_id.strip()})</span>"

    def test_api_connection(config_state):
        """测试API连接"""
        provider = config_state.get("selected_provider", "OpenAI")

        # 检查API密钥
        api_keys = config_state.get("api_keys", {})
        if provider not in api_keys:
            return f"<span style='color: red;'>❌ 请先为{provider}配置API密钥</span>"

        # 准备测试消息
        test_messages = [
            {"role": "system", "content": "你是一个AI助手，请简短回复。"},
            {"role": "user", "content": "请回复'连接测试成功'，不要添加其他内容。"}
        ]

        # 调用API测试
        response = call_ai_api(test_messages, config_state, test_mode=True)

        if "❌" in response:
            return f"<span style='color: red;'>{response}</span>"
        elif "连接测试成功" in response or "成功" in response or len(response.strip()) > 0:
            return f"<span style='color: green;'>✅ API连接测试成功</span><br><small>响应: {response[:50]}...</small>"
        else:
            return f"<span style='color: orange;'>⚠️ 连接可能有问题，收到空响应</span>"

    def apply_local_config(local_endpoint, local_model, config_state):
        """应用本地模型配置"""
        # 验证本地端点URL
        is_valid, message = validate_url(local_endpoint)
        if not is_valid:
            return config_state, f"<span style='color: red;'>❌ {message}</span>"

        # 更新配置为本地模型
        config_state.update({
            "selected_provider": "本地模型",
            "selected_model": "custom-local-model",
            "custom_api_base": local_endpoint.strip(),
            "custom_model_name": local_model.strip(),
        })

        # 设置默认的本地API密钥
        if "api_keys" not in config_state:
            config_state["api_keys"] = {}
        config_state["api_keys"]["本地模型"] = "local-key"

        save_config(config_state)
        return config_state, "<span style='color: green;'>✅ 本地模型配置已应用</span>"

    def load_template_config(provider, config_state):
        """加载模板配置"""
        if provider not in AI_PROVIDERS:
            return config_state, "<span style='color: red;'>❌ 无效的服务商</span>"

        provider_config = AI_PROVIDERS[provider]
        config_state.update({
            "selected_provider": provider,
            "selected_model": provider_config["test_model"] or provider_config["models"][0],
            "custom_api_base": "",  # 使用默认端点
            "custom_model_name": "",  # 使用预设模型
            "temperature": 0.7,
            "max_tokens": 400000,
            "timeout": 30
        })

        save_config(config_state)
        return config_state, f"<span style='color: green;'>✅ 已加载{provider}的模板配置</span>"

    def reset_config():
        """重置配置"""
        default_config = DEFAULT_CONFIG.copy()
        save_config(default_config)
        return (
            default_config,
            default_config["selected_provider"],
            default_config["model_id"],  # 使用新的model_id字段
            default_config["custom_api_base"],
            default_config["temperature"],
            default_config["max_tokens"],
            default_config["timeout"],
            "<span style='color: green;'>✅ 配置已重置为默认值</span>"
        )

    def clear_chat():
        """清空对话"""
        return []

    def set_quick_question(question):
        """设置快捷问题"""
        return question

    # 创建状态管理
    config_state = gr.State(value=config)

    # 绑定事件
    # 聊天功能
    send_btn.click(
        smart_chat,
        inputs=[msg_input, chatbot, config_state],
        outputs=[chatbot, msg_input]
    )

    msg_input.submit(
        smart_chat,
        inputs=[msg_input, chatbot, config_state],
        outputs=[chatbot, msg_input]
    )

    clear_btn.click(
        clear_chat,
        outputs=[chatbot]
    )

    # 快捷问题按钮
    quick_btn1.click(
        set_quick_question,
        inputs=[gr.State("如何选择合适的学习率？")],
        outputs=[msg_input]
    )

    quick_btn2.click(
        set_quick_question,
        inputs=[gr.State("LoRA参数推荐设置是什么？")],
        outputs=[msg_input]
    )

    quick_btn3.click(
        set_quick_question,
        inputs=[gr.State("如何处理显存不足问题？")],
        outputs=[msg_input]
    )

    quick_btn4.click(
        set_quick_question,
        inputs=[gr.State("训练数据集如何准备？")],
        outputs=[msg_input]
    )

    # AI模型配置事件
    # 服务商切换时更新信息
    provider_dropdown.change(
        update_provider_info,
        inputs=[provider_dropdown],
        outputs=[provider_info, current_endpoint]
    )

    # 模型ID验证
    model_id_input.change(
        validate_model_id,
        inputs=[model_id_input, provider_dropdown],
        outputs=[model_status]
    )

    model_validate_btn.click(
        validate_model_id,
        inputs=[model_id_input, provider_dropdown],
        outputs=[model_status]
    )

    # API密钥验证
    api_key_input.change(
        validate_api_key_input,
        inputs=[provider_dropdown, api_key_input],
        outputs=[api_key_status]
    )

    # URL验证
    custom_api_base.change(
        validate_url_input,
        inputs=[custom_api_base],
        outputs=[url_status]
    )

    # API密钥管理
    save_key_btn.click(
        save_api_key,
        inputs=[provider_dropdown, api_key_input, config_state],
        outputs=[config_state, config_status]
    )

    validate_key_btn.click(
        validate_api_key_input,
        inputs=[provider_dropdown, api_key_input],
        outputs=[api_key_status]
    )

    clear_key_btn.click(
        clear_api_key,
        inputs=[provider_dropdown, config_state],
        outputs=[config_state, config_status, api_key_input]
    )

    # 快速模型选择按钮事件
    # OpenAI模型
    gpt4_btn.click(lambda: "gpt-4", outputs=[model_id_input])
    gpt4o_btn.click(lambda: "gpt-4o", outputs=[model_id_input])
    gpt35_btn.click(lambda: "gpt-3.5-turbo", outputs=[model_id_input])
    gpt4o_mini_btn.click(lambda: "gpt-4o-mini", outputs=[model_id_input])

    # Claude模型
    claude_sonnet_btn.click(lambda: "claude-3-5-sonnet-20241022", outputs=[model_id_input])
    claude_haiku_btn.click(lambda: "claude-3-5-haiku-20241022", outputs=[model_id_input])
    claude_opus_btn.click(lambda: "claude-3-opus-20240229", outputs=[model_id_input])

    # 智谱AI模型
    glm4_btn.click(lambda: "glm-4", outputs=[model_id_input])
    glm4_air_btn.click(lambda: "glm-4-air", outputs=[model_id_input])
    glm4_flash_btn.click(lambda: "glm-4-flash", outputs=[model_id_input])
    glm4v_btn.click(lambda: "glm-4v", outputs=[model_id_input])

    # OpenRouter模型
    or_gpt4_btn.click(lambda: "gpt-4", outputs=[model_id_input])
    or_claude_btn.click(lambda: "claude-3-5-sonnet", outputs=[model_id_input])
    or_llama_btn.click(lambda: "llama-2-70b-chat", outputs=[model_id_input])
    or_mistral_btn.click(lambda: "mistral-7b-instruct", outputs=[model_id_input])

    # Poe模型
    poe_claude_btn.click(lambda: "Claude-Sonnet-4", outputs=[model_id_input])
    poe_grok_btn.click(lambda: "Grok-4", outputs=[model_id_input])
    poe_gpt4_btn.click(lambda: "GPT-4-Turbo", outputs=[model_id_input])
    poe_haiku_btn.click(lambda: "Claude-Haiku", outputs=[model_id_input])

    # 其他模型
    qwen_turbo_btn.click(lambda: "qwen-turbo", outputs=[model_id_input])
    qwen_plus_btn.click(lambda: "qwen-plus", outputs=[model_id_input])
    gemini_pro_btn.click(lambda: "gemini-pro", outputs=[model_id_input])
    gemini_flash_btn.click(lambda: "gemini-1.5-flash", outputs=[model_id_input])

    # 本地模型示例
    llama2_btn.click(lambda: "llama-2-7b-chat", outputs=[model_id_input])
    chatglm_btn.click(lambda: "chatglm3-6b", outputs=[model_id_input])
    baichuan_btn.click(lambda: "baichuan2-7b-chat", outputs=[model_id_input])
    custom_btn.click(lambda: "my-custom-model", outputs=[model_id_input])

    # 配置管理
    save_config_btn.click(
        save_full_config,
        inputs=[
            provider_dropdown, model_id_input, custom_api_base,
            temperature_slider, max_tokens_slider, timeout_slider, config_state
        ],
        outputs=[config_state, config_status]
    )

    test_connection_btn.click(
        test_api_connection,
        inputs=[config_state],
        outputs=[config_status]
    )

    load_template_btn.click(
        load_template_config,
        inputs=[provider_dropdown, config_state],
        outputs=[config_state, config_status]
    )

    reset_config_btn.click(
        reset_config,
        outputs=[
            config_state, provider_dropdown, model_id_input, custom_api_base,
            temperature_slider, max_tokens_slider, timeout_slider, config_status
        ]
    )

    # 本地模型配置
    apply_local_config_btn.click(
        apply_local_config,
        inputs=[local_endpoint_input, local_model_input, config_state],
        outputs=[config_state, config_status]
    )

    # 训练工具事件
    get_recommendations_btn.click(
        get_hardware_recommendations,
        inputs=[gpu_memory_input],
        outputs=[recommendations_output]
    )

    analyze_log_btn.click(
        analyze_training_log,
        inputs=[log_input],
        outputs=[log_analysis_output]
    )

    return {
        # 聊天组件
        "chatbot": chatbot,
        "msg_input": msg_input,
        "send_btn": send_btn,
        "clear_btn": clear_btn,
        "quick_btn1": quick_btn1,
        "quick_btn2": quick_btn2,
        "quick_btn3": quick_btn3,
        "quick_btn4": quick_btn4,

        # AI配置组件 - 基础
        "provider_dropdown": provider_dropdown,
        "provider_info": provider_info,
        "model_id_input": model_id_input,
        "model_validate_btn": model_validate_btn,
        "model_status": model_status,

        # 快速选择按钮
        "gpt4_btn": gpt4_btn,
        "gpt4o_btn": gpt4o_btn,
        "gpt35_btn": gpt35_btn,
        "gpt4o_mini_btn": gpt4o_mini_btn,
        "claude_sonnet_btn": claude_sonnet_btn,
        "claude_haiku_btn": claude_haiku_btn,
        "claude_opus_btn": claude_opus_btn,
        "glm4_btn": glm4_btn,
        "glm4_air_btn": glm4_air_btn,
        "glm4_flash_btn": glm4_flash_btn,
        "glm4v_btn": glm4v_btn,

        # OpenRouter按钮
        "or_gpt4_btn": or_gpt4_btn,
        "or_claude_btn": or_claude_btn,
        "or_llama_btn": or_llama_btn,
        "or_mistral_btn": or_mistral_btn,

        # Poe按钮
        "poe_claude_btn": poe_claude_btn,
        "poe_grok_btn": poe_grok_btn,
        "poe_gpt4_btn": poe_gpt4_btn,
        "poe_haiku_btn": poe_haiku_btn,

        # 其他按钮
        "qwen_turbo_btn": qwen_turbo_btn,
        "qwen_plus_btn": qwen_plus_btn,
        "gemini_pro_btn": gemini_pro_btn,
        "gemini_flash_btn": gemini_flash_btn,
        "llama2_btn": llama2_btn,
        "chatglm_btn": chatglm_btn,
        "baichuan_btn": baichuan_btn,
        "custom_btn": custom_btn,

        # AI配置组件 - API密钥
        "api_key_input": api_key_input,
        "api_key_status": api_key_status,
        "save_key_btn": save_key_btn,
        "validate_key_btn": validate_key_btn,
        "clear_key_btn": clear_key_btn,

        # AI配置组件 - API端点
        "custom_api_base": custom_api_base,
        "url_status": url_status,
        "current_endpoint": current_endpoint,

        # AI配置组件 - 参数
        "temperature_slider": temperature_slider,
        "max_tokens_slider": max_tokens_slider,
        "timeout_slider": timeout_slider,

        # AI配置组件 - 操作
        "config_status": config_status,
        "save_config_btn": save_config_btn,
        "test_connection_btn": test_connection_btn,
        "load_template_btn": load_template_btn,
        "reset_config_btn": reset_config_btn,

        # 本地模型配置
        "local_endpoint_input": local_endpoint_input,
        "local_model_input": local_model_input,
        "apply_local_config_btn": apply_local_config_btn,

        # 训练工具组件
        "gpu_memory_input": gpu_memory_input,
        "get_recommendations_btn": get_recommendations_btn,
        "recommendations_output": recommendations_output,
        "log_input": log_input,
        "analyze_log_btn": analyze_log_btn,
        "log_analysis_output": log_analysis_output,

        # 状态管理
        "config_state": config_state
    }
