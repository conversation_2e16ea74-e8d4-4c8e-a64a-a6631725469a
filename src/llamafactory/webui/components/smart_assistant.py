# Copyright 2025 the LlamaFactory team.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import json
import os
import time
import requests
import threading
import traceback
from typing import Any, Dict, List, Optional, Tuple, Union, TYPE_CHECKING

from ...extras.packages import is_gradio_available

if is_gradio_available():
    import gradio as gr

if TYPE_CHECKING:
    from gradio.components import Component
    from ..engine import Engine

# 配置文件路径
CONFIG_FILE = "smart_assistant_config.json"

# AI模型配置 - 支持更多模型和服务商
AI_MODELS = {
    "OpenAI": {
        "gpt-4": "GPT-4",
        "gpt-4-turbo": "GPT-4 Turbo", 
        "gpt-4o": "GPT-4o",
        "gpt-3.5-turbo": "GPT-3.5 Turbo"
    },
    "Claude": {
        "claude-3-5-sonnet-20241022": "Claude 3.5 Sonnet",
        "claude-3-5-haiku-20241022": "Claude 3.5 Haiku",
        "claude-3-opus-20240229": "Claude 3 Opus"
    },
    "Gemini": {
        "gemini-pro": "Gemini Pro",
        "gemini-pro-vision": "Gemini Pro Vision",
        "gemini-1.5-pro": "Gemini 1.5 Pro",
        "gemini-1.5-flash": "Gemini 1.5 Flash"
    },
    "xAI": {
        "grok-beta": "Grok Beta",
        "grok-vision-beta": "Grok Vision Beta"
    },
    "智谱AI": {
        "glm-4": "GLM-4",
        "glm-4-air": "GLM-4 Air",
        "glm-4-flash": "GLM-4 Flash",
        "glm-4-plus": "GLM-4 Plus"
    },
    "Qwen": {
        "qwen-turbo": "Qwen Turbo",
        "qwen-plus": "Qwen Plus", 
        "qwen-max": "Qwen Max",
        "qwen-long": "Qwen Long"
    },
    "百度文心": {
        "ernie-4.0": "文心一言 4.0",
        "ernie-3.5": "文心一言 3.5",
        "ernie-turbo": "文心一言 Turbo"
    },
    "讯飞星火": {
        "spark-3.5": "星火 3.5",
        "spark-pro": "星火 Pro",
        "spark-lite": "星火 Lite"
    },
    "本地模型": {
        "custom": "自定义本地模型"
    }
}

# API端点配置
API_ENDPOINTS = {
    "OpenAI": "https://api.openai.com/v1",
    "Claude": "https://api.anthropic.com/v1", 
    "Gemini": "https://generativelanguage.googleapis.com/v1",
    "xAI": "https://api.x.ai/v1",
    "智谱AI": "https://open.bigmodel.cn/api/paas/v4",
    "Qwen": "https://dashscope.aliyuncs.com/api/v1",
    "百度文心": "https://aip.baidubce.com/rpc/2.0/ai_custom/v1",
    "讯飞星火": "https://spark-api.xf-yun.com/v1",
    "本地模型": "http://localhost:8000/v1"
}

# 常见问题快捷按钮
QUICK_QUESTIONS = [
    "🎯 如何选择合适的学习率？",
    "🔧 LoRA参数推荐设置是什么？", 
    "⚡ QLoRA和LoRA有什么区别？",
    "💾 如何处理显存不足的问题？",
    "📊 训练数据集应该如何准备？",
    "📈 如何判断模型是否过拟合？",
    "🎪 微调后的模型如何评估效果？",
    "⚙️ 如何选择合适的批次大小？",
    "🚀 如何优化训练速度？",
    "🔍 如何解读训练日志？"
]

# AI助手的系统提示词
SYSTEM_PROMPT = """你是Magic Lab项目的专业AI助手，具有以下专业能力：

## 核心职责
1. **模型微调专家**：提供精确的参数配置建议，包括学习率、批次大小、LoRA参数等
2. **故障排除专家**：快速诊断训练过程中的问题，提供具体解决方案
3. **最佳实践指导**：基于项目经验提供优化建议和最佳实践
4. **数据处理专家**：指导数据集准备、格式化和预处理

## 专业知识领域
- Magic Lab项目架构和功能
- 大语言模型微调技术（SFT、DPO、PPO、LoRA、QLoRA等）
- 训练参数优化和硬件配置
- 数据集准备和格式要求
- 常见错误诊断和解决方案

## 回答风格
- 专业准确：提供技术精确的建议
- 实用导向：给出可直接执行的解决方案
- 结构清晰：使用标题、列表等格式化输出
- 代码示例：提供具体的配置文件和命令示例

## 特殊能力
- 根据用户硬件配置推荐最优参数
- 解释训练日志和错误信息
- 提供配置文件模板
- 实时监控训练状态并给出优化建议

请始终以专业、友好的态度回答用户问题，并提供具体可行的解决方案。"""

def load_config() -> Dict[str, Any]:
    """加载AI助手配置"""
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        print(f"加载配置失败: {e}")
    
    # 默认配置
    return {
        "selected_provider": "OpenAI",
        "selected_model": "gpt-3.5-turbo",
        "api_keys": {},
        "api_base_urls": {},
        "chat_history": [],
        "temperature": 0.7,
        "max_tokens": 2000,
        "system_prompt": SYSTEM_PROMPT,
        "custom_model_name": "",
        "local_api_url": "http://localhost:8000/v1"
    }

def save_config(config: Dict[str, Any]) -> None:
    """保存AI助手配置"""
    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"保存配置失败: {e}")

def get_model_choices() -> List[str]:
    """获取模型选择列表"""
    choices = []
    for provider, models in AI_MODELS.items():
        for model_id, model_name in models.items():
            choices.append(f"{provider}: {model_name}")
    return choices

def parse_model_choice(choice: str) -> Tuple[str, str]:
    """解析模型选择"""
    if ": " in choice:
        provider, model_name = choice.split(": ", 1)
        # 找到对应的model_id
        for model_id, name in AI_MODELS.get(provider, {}).items():
            if name == model_name:
                return provider, model_id
    return "OpenAI", "gpt-3.5-turbo"

class AIClient:
    """AI客户端类，处理与各种AI服务的通信"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.timeout = 30
    
    def call_openai_api(self, messages: List[Dict], config: Dict) -> str:
        """调用OpenAI API"""
        try:
            headers = {
                "Authorization": f"Bearer {config['api_key']}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": config['model'],
                "messages": messages,
                "temperature": config.get('temperature', 0.7),
                "max_tokens": config.get('max_tokens', 2000)
            }
            
            response = self.session.post(
                f"{config['api_base']}/chat/completions",
                headers=headers,
                json=data
            )
            response.raise_for_status()
            
            result = response.json()
            return result['choices'][0]['message']['content']
            
        except Exception as e:
            return f"API调用失败: {str(e)}"
    
    def call_claude_api(self, messages: List[Dict], config: Dict) -> str:
        """调用Claude API"""
        try:
            headers = {
                "x-api-key": config['api_key'],
                "Content-Type": "application/json",
                "anthropic-version": "2023-06-01"
            }
            
            # 转换消息格式
            claude_messages = []
            system_message = ""
            
            for msg in messages:
                if msg['role'] == 'system':
                    system_message = msg['content']
                else:
                    claude_messages.append(msg)
            
            data = {
                "model": config['model'],
                "messages": claude_messages,
                "max_tokens": config.get('max_tokens', 2000),
                "temperature": config.get('temperature', 0.7)
            }
            
            if system_message:
                data["system"] = system_message
            
            response = self.session.post(
                f"{config['api_base']}/messages",
                headers=headers,
                json=data
            )
            response.raise_for_status()
            
            result = response.json()
            return result['content'][0]['text']
            
        except Exception as e:
            return f"Claude API调用失败: {str(e)}"
    
    def call_custom_api(self, messages: List[Dict], config: Dict) -> str:
        """调用自定义API（兼容OpenAI格式）"""
        return self.call_openai_api(messages, config)

# 全局AI客户端实例
ai_client = AIClient()

def get_hardware_recommendations(gpu_memory: int) -> Dict[str, Any]:
    """根据GPU显存推荐训练参数"""
    if gpu_memory <= 4:
        return {
            "quantization_bit": 4,
            "lora_rank": 8,
            "per_device_train_batch_size": 1,
            "gradient_accumulation_steps": 16,
            "cutoff_len": 512,
            "recommendation": "4GB显存配置：使用4位量化和小批次训练"
        }
    elif gpu_memory <= 8:
        return {
            "quantization_bit": 4,
            "lora_rank": 16,
            "per_device_train_batch_size": 2,
            "gradient_accumulation_steps": 8,
            "cutoff_len": 1024,
            "recommendation": "8GB显存配置：平衡的量化和批次设置"
        }
    elif gpu_memory <= 16:
        return {
            "lora_rank": 32,
            "per_device_train_batch_size": 4,
            "gradient_accumulation_steps": 4,
            "cutoff_len": 2048,
            "recommendation": "16GB显存配置：标准训练配置"
        }
    else:
        return {
            "lora_rank": 64,
            "per_device_train_batch_size": 8,
            "gradient_accumulation_steps": 2,
            "cutoff_len": 4096,
            "recommendation": "24GB+显存配置：高质量训练配置"
        }

def analyze_training_log(log_content: str) -> str:
    """分析训练日志并提供建议"""
    analysis = []

    if "CUDA out of memory" in log_content:
        analysis.append("🚨 **显存不足问题**")
        analysis.append("- 减少批次大小：`per_device_train_batch_size: 1`")
        analysis.append("- 增加梯度累积：`gradient_accumulation_steps: 16`")
        analysis.append("- 启用量化：`quantization_bit: 4`")
        analysis.append("- 启用梯度检查点：`gradient_checkpointing: true`")

    if "loss" in log_content.lower():
        analysis.append("📈 **训练损失分析**")
        analysis.append("- 监控损失下降趋势")
        analysis.append("- 如果损失不下降，考虑调整学习率")
        analysis.append("- 如果损失震荡，可能需要降低学习率")

    if "nan" in log_content.lower():
        analysis.append("⚠️ **NaN值问题**")
        analysis.append("- 降低学习率：`learning_rate: 1e-5`")
        analysis.append("- 启用梯度裁剪：`max_grad_norm: 1.0`")
        analysis.append("- 检查数据质量")

    if not analysis:
        analysis.append("✅ **日志看起来正常**")
        analysis.append("- 训练进展顺利")
        analysis.append("- 继续监控训练指标")

    return "\n".join(analysis)

def generate_config_template(task_type: str, hardware: str) -> str:
    """生成配置文件模板"""
    templates = {
        "对话微调": {
            "4GB": """
# 4GB显存对话微调配置
model_name_or_path: Qwen/Qwen2.5-7B-Instruct
quantization_bit: 4
quantization_method: bnb
stage: sft
do_train: true
finetuning_type: lora
lora_rank: 8
lora_alpha: 16
dataset: alpaca_en_demo
template: qwen
cutoff_len: 512
per_device_train_batch_size: 1
gradient_accumulation_steps: 16
learning_rate: 2e-4
num_train_epochs: 3.0
output_dir: saves/chat_4gb
""",
            "8GB": """
# 8GB显存对话微调配置
model_name_or_path: Qwen/Qwen2.5-7B-Instruct
quantization_bit: 4
stage: sft
do_train: true
finetuning_type: lora
lora_rank: 16
lora_alpha: 32
dataset: alpaca_en_demo
template: qwen
cutoff_len: 1024
per_device_train_batch_size: 2
gradient_accumulation_steps: 8
learning_rate: 1e-4
num_train_epochs: 3.0
output_dir: saves/chat_8gb
""",
            "16GB": """
# 16GB显存对话微调配置
model_name_or_path: Qwen/Qwen2.5-7B-Instruct
stage: sft
do_train: true
finetuning_type: lora
lora_rank: 32
lora_alpha: 64
dataset: alpaca_en_demo
template: qwen
cutoff_len: 2048
per_device_train_batch_size: 4
gradient_accumulation_steps: 4
learning_rate: 5e-5
num_train_epochs: 3.0
bf16: true
output_dir: saves/chat_16gb
"""
        }
    }

    return templates.get(task_type, {}).get(hardware, "# 配置模板不存在")

def call_ai_api(messages: List[Dict], config: Dict) -> str:
    """统一的AI API调用接口"""
    try:
        provider = config.get('selected_provider', 'OpenAI')
        model = config.get('selected_model', 'gpt-3.5-turbo')
        api_key = config.get('api_keys', {}).get(provider, '')
        api_base = config.get('api_base_urls', {}).get(provider, API_ENDPOINTS.get(provider, ''))

        if not api_key:
            return "❌ 请先配置API Key"

        api_config = {
            'model': model,
            'api_key': api_key,
            'api_base': api_base,
            'temperature': config.get('temperature', 0.7),
            'max_tokens': config.get('max_tokens', 2000)
        }

        if provider == "Claude":
            return ai_client.call_claude_api(messages, api_config)
        else:
            # 其他提供商都使用OpenAI兼容格式
            return ai_client.call_openai_api(messages, api_config)

    except Exception as e:
        return f"❌ API调用失败: {str(e)}"

def create_smart_assistant() -> Dict[str, "Component"]:
    """创建智能AI助手组件"""

    # 加载配置
    config = load_config()

    # 创建一个简单的智能助手界面
    with gr.Blocks(title="智能AI助手", css="""
        .gradio-container {
            max-width: 1200px !important;
            margin: 0 auto !important;
        }
        .tab-nav {
            background: linear-gradient(90deg, #f8fafc, #e2e8f0) !important;
        }
        .selected {
            background: linear-gradient(90deg, #3b82f6, #1d4ed8) !important;
            color: white !important;
        }
    """) as smart_assistant_interface:

        # 状态管理
        assistant_state = gr.State(value=config)

        # 简化的智能助手界面
        gr.HTML("<h2 style='text-align: center; color: #1f2937;'>🤖 Magic Lab智能助手</h2>")

        with gr.Tabs():
            # 对话标签页
            with gr.Tab("💬 智能对话"):
                # 聊天界面
                chatbot = gr.Chatbot(
                    height=400,
                    show_copy_button=True,
                    type="messages"
                )

                # 快捷问题
                gr.HTML("<p><strong>🚀 快捷问题：</strong></p>")
                with gr.Row():
                    quick_btn1 = gr.Button("🎯 如何选择合适的学习率？", size="sm")
                    quick_btn2 = gr.Button("🔧 LoRA参数推荐设置？", size="sm")

                with gr.Row():
                    quick_btn3 = gr.Button("💾 如何处理显存不足？", size="sm")
                    quick_btn4 = gr.Button("📊 训练数据集如何准备？", size="sm")

                # 输入区域
                with gr.Row():
                    msg_input = gr.Textbox(
                        placeholder="请输入您的问题...",
                        container=False,
                        scale=4
                    )
                    send_btn = gr.Button("发送", variant="primary", scale=1)

                clear_btn = gr.Button("🗑️ 清空对话")

            # 配置标签页
            with gr.Tab("⚙️ 配置设置"):
                gr.HTML("<h4>🤖 AI模型配置</h4>")

                model_name_input = gr.Textbox(
                    value=config.get('custom_model_name', ''),
                    label="模型名称",
                    placeholder="如：gpt-4, claude-3-5-sonnet-20241022, glm-4",
                    info="支持OpenAI、Claude、Gemini、智谱AI、Qwen等主流模型"
                )

                api_key_input = gr.Textbox(
                    label="API Key",
                    type="password",
                    placeholder="请输入API密钥"
                )

                api_base_input = gr.Textbox(
                    label="API Base URL (可选)",
                    placeholder="自定义API端点地址"
                )

                with gr.Row():
                    temperature_slider = gr.Slider(
                        minimum=0.0,
                        maximum=2.0,
                        value=config.get('temperature', 0.7),
                        step=0.1,
                        label="温度参数"
                    )
                    max_tokens_slider = gr.Slider(
                        minimum=100,
                        maximum=4000,
                        value=config.get('max_tokens', 2000),
                        step=100,
                        label="最大Token数"
                    )

                save_config_btn = gr.Button("💾 保存配置", variant="primary")
                config_status = gr.HTML()

            # 工具标签页
            with gr.Tab("🛠️ 训练工具"):
                gr.HTML("<h4>🔧 硬件配置推荐</h4>")

                gpu_memory_input = gr.Number(
                    label="GPU显存大小 (GB)",
                    value=8,
                    minimum=1,
                    maximum=80
                )

                get_recommendations_btn = gr.Button("获取推荐配置")
                recommendations_output = gr.Markdown()

                gr.HTML("<h4>📊 训练日志分析</h4>")

                log_input = gr.Textbox(
                    label="粘贴训练日志",
                    lines=5,
                    placeholder="请粘贴训练日志内容..."
                )

                analyze_log_btn = gr.Button("分析日志")
                log_analysis_output = gr.Markdown()

        # 简化的事件处理函数
        def toggle_assistant():
            """切换助手显示状态"""
            return gr.update(visible=True)

        def close_assistant():
            """关闭助手"""
            return gr.update(visible=False)

        def save_assistant_config(model_name, api_key, api_base, temperature, max_tokens, state):
            """保存AI助手配置"""
            # 根据模型名称推断提供商
            provider = "OpenAI"  # 默认
            if model_name:
                model_name_lower = model_name.lower()
                if "claude" in model_name_lower:
                    provider = "Claude"
                elif "gemini" in model_name_lower:
                    provider = "Gemini"
                elif "glm" in model_name_lower:
                    provider = "智谱AI"
                elif "qwen" in model_name_lower:
                    provider = "Qwen"

            state["selected_provider"] = provider
            state["selected_model"] = model_name.strip() if model_name else "gpt-3.5-turbo"
            state["temperature"] = temperature
            state["max_tokens"] = int(max_tokens)

            if api_key:
                if "api_keys" not in state:
                    state["api_keys"] = {}
                state["api_keys"][provider] = api_key

            if api_base:
                if "api_base_urls" not in state:
                    state["api_base_urls"] = {}
                state["api_base_urls"][provider] = api_base

            save_config(state)
            return state, "✅ 配置已保存！"

        def smart_chat_response(message, history, state):
            """智能聊天响应"""
            if not message.strip():
                return history, ""

            # 简化的消息处理
            history = history or []

            # 准备API调用的消息格式
            messages = [{"role": "system", "content": SYSTEM_PROMPT}]

            # 添加历史对话（最近5轮）
            for msg in history[-10:]:
                if isinstance(msg, dict):
                    messages.append(msg)

            # 添加当前用户消息
            messages.append({"role": "user", "content": message})

            # 调用AI API
            try:
                response = call_ai_api(messages, state)
                history.append({"role": "user", "content": message})
                history.append({"role": "assistant", "content": response})
            except Exception as e:
                error_msg = f"❌ 抱歉，处理您的请求时出现错误：{str(e)}"
                history.append({"role": "user", "content": message})
                history.append({"role": "assistant", "content": error_msg})

            return history, ""

        def clear_chat_history():
            """清空聊天历史"""
            return []

        def quick_question_handler(question):
            """处理快捷问题"""
            clean_question = question.split(" ", 1)[1] if " " in question else question
            return clean_question

        def get_hardware_recommendations_handler(gpu_memory):
            """获取硬件推荐配置"""
            try:
                recommendations = get_hardware_recommendations(int(gpu_memory))

                output = f"""## 🎯 {recommendations['recommendation']}

### 📋 推荐参数配置

```yaml
# GPU显存: {gpu_memory}GB 优化配置
"""

                for key, value in recommendations.items():
                    if key != 'recommendation':
                        output += f"{key}: {value}\n"

                output += "```"

                return output

            except Exception as e:
                return f"❌ 获取推荐配置失败: {str(e)}"

        def analyze_training_log_handler(log_content):
            """分析训练日志"""
            if not log_content.strip():
                return "请输入训练日志内容"

            try:
                analysis = analyze_training_log(log_content)
                return f"## 📊 训练日志分析结果\n\n{analysis}"
            except Exception as e:
                return f"❌ 日志分析失败: {str(e)}"

        # 事件绑定已简化，移除浮窗相关功能

        # 保存配置
        save_config_btn.click(
            save_assistant_config,
            inputs=[
                model_name_input,
                api_key_input,
                api_base_input,
                temperature_slider,
                max_tokens_slider,
                assistant_state
            ],
            outputs=[assistant_state, config_status]
        )

        # 聊天功能
        send_btn.click(
            smart_chat_response,
            inputs=[msg_input, chatbot, assistant_state],
            outputs=[chatbot, msg_input]
        )

        msg_input.submit(
            smart_chat_response,
            inputs=[msg_input, chatbot, assistant_state],
            outputs=[chatbot, msg_input]
        )

        # 快捷问题按钮
        quick_btn1.click(
            quick_question_handler,
            inputs=[gr.State("🎯 如何选择合适的学习率？")],
            outputs=[msg_input]
        )

        quick_btn2.click(
            quick_question_handler,
            inputs=[gr.State("🔧 LoRA参数推荐设置是什么？")],
            outputs=[msg_input]
        )

        quick_btn3.click(
            quick_question_handler,
            inputs=[gr.State("💾 如何处理显存不足的问题？")],
            outputs=[msg_input]
        )

        quick_btn4.click(
            quick_question_handler,
            inputs=[gr.State("📊 训练数据集如何准备？")],
            outputs=[msg_input]
        )

        # 清空对话
        clear_btn.click(
            clear_chat_history,
            outputs=[chatbot]
        )

        # 工具功能
        get_recommendations_btn.click(
            get_hardware_recommendations_handler,
            inputs=[gpu_memory_input],
            outputs=[recommendations_output]
        )

        analyze_log_btn.click(
            analyze_training_log_handler,
            inputs=[log_input],
            outputs=[log_analysis_output]
        )

    return {
        "smart_assistant_interface": smart_assistant_interface
    }
