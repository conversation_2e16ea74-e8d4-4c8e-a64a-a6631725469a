# Copyright 2025 the LlamaFactory team.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import asyncio
import json
import time
from typing import Any, AsyncGenerator, Dict, List, Optional

import httpx


class AIClient:
    """AI模型调用客户端"""
    
    def __init__(self):
        self.timeout = 60.0
        self.max_retries = 3
        
    async def call_openai(
        self,
        messages: List[Dict[str, str]],
        model: str,
        api_key: str,
        api_base: Optional[str] = None,
        stream: bool = True
    ) -> AsyncGenerator[str, None]:
        """调用OpenAI API"""
        url = f"{api_base or 'https://api.openai.com/v1'}/chat/completions"
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": model,
            "messages": messages,
            "stream": stream,
            "temperature": 0.7,
            "max_tokens": 2000
        }
        
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                if stream:
                    async with client.stream("POST", url, headers=headers, json=data) as response:
                        if response.status_code != 200:
                            error_text = await response.aread()
                            raise Exception(f"API调用失败: {response.status_code} - {error_text.decode()}")
                        
                        async for line in response.aiter_lines():
                            if line.startswith("data: "):
                                line = line[6:]
                                if line.strip() == "[DONE]":
                                    break
                                try:
                                    chunk = json.loads(line)
                                    if "choices" in chunk and len(chunk["choices"]) > 0:
                                        delta = chunk["choices"][0].get("delta", {})
                                        if "content" in delta:
                                            yield delta["content"]
                                except json.JSONDecodeError:
                                    continue
                else:
                    response = await client.post(url, headers=headers, json=data)
                    if response.status_code != 200:
                        raise Exception(f"API调用失败: {response.status_code} - {response.text}")
                    
                    result = response.json()
                    if "choices" in result and len(result["choices"]) > 0:
                        yield result["choices"][0]["message"]["content"]
                        
            except Exception as e:
                yield f"❌ OpenAI API调用失败: {str(e)}"
    
    async def call_claude(
        self,
        messages: List[Dict[str, str]],
        model: str,
        api_key: str,
        api_base: Optional[str] = None,
        stream: bool = True
    ) -> AsyncGenerator[str, None]:
        """调用Claude API"""
        url = f"{api_base or 'https://api.anthropic.com'}/v1/messages"
        headers = {
            "x-api-key": api_key,
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01"
        }
        
        # 转换消息格式
        claude_messages = []
        system_message = ""
        
        for msg in messages:
            if msg["role"] == "system":
                system_message = msg["content"]
            else:
                claude_messages.append(msg)
        
        data = {
            "model": model,
            "messages": claude_messages,
            "max_tokens": 2000,
            "stream": stream
        }
        
        if system_message:
            data["system"] = system_message
        
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                if stream:
                    async with client.stream("POST", url, headers=headers, json=data) as response:
                        if response.status_code != 200:
                            error_text = await response.aread()
                            raise Exception(f"API调用失败: {response.status_code} - {error_text.decode()}")
                        
                        async for line in response.aiter_lines():
                            if line.startswith("data: "):
                                line = line[6:]
                                try:
                                    chunk = json.loads(line)
                                    if chunk.get("type") == "content_block_delta":
                                        delta = chunk.get("delta", {})
                                        if "text" in delta:
                                            yield delta["text"]
                                except json.JSONDecodeError:
                                    continue
                else:
                    response = await client.post(url, headers=headers, json=data)
                    if response.status_code != 200:
                        raise Exception(f"API调用失败: {response.status_code} - {response.text}")
                    
                    result = response.json()
                    if "content" in result and len(result["content"]) > 0:
                        yield result["content"][0]["text"]
                        
            except Exception as e:
                yield f"❌ Claude API调用失败: {str(e)}"
    
    async def call_gemini(
        self,
        messages: List[Dict[str, str]],
        model: str,
        api_key: str,
        api_base: Optional[str] = None,
        stream: bool = True
    ) -> AsyncGenerator[str, None]:
        """调用Gemini API"""
        url = f"{api_base or 'https://generativelanguage.googleapis.com'}/v1beta/models/{model}:streamGenerateContent"
        if not stream:
            url = url.replace(":streamGenerateContent", ":generateContent")
        
        url += f"?key={api_key}"
        
        headers = {"Content-Type": "application/json"}
        
        # 转换消息格式
        contents = []
        for msg in messages:
            if msg["role"] == "system":
                continue  # Gemini不支持system消息
            role = "user" if msg["role"] == "user" else "model"
            contents.append({
                "role": role,
                "parts": [{"text": msg["content"]}]
            })
        
        data = {
            "contents": contents,
            "generationConfig": {
                "temperature": 0.7,
                "maxOutputTokens": 2000
            }
        }
        
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                response = await client.post(url, headers=headers, json=data)
                if response.status_code != 200:
                    raise Exception(f"API调用失败: {response.status_code} - {response.text}")
                
                if stream:
                    # Gemini流式响应处理
                    for line in response.text.split('\n'):
                        if line.strip():
                            try:
                                chunk = json.loads(line)
                                if "candidates" in chunk:
                                    for candidate in chunk["candidates"]:
                                        if "content" in candidate and "parts" in candidate["content"]:
                                            for part in candidate["content"]["parts"]:
                                                if "text" in part:
                                                    yield part["text"]
                            except json.JSONDecodeError:
                                continue
                else:
                    result = response.json()
                    if "candidates" in result and len(result["candidates"]) > 0:
                        candidate = result["candidates"][0]
                        if "content" in candidate and "parts" in candidate["content"]:
                            for part in candidate["content"]["parts"]:
                                if "text" in part:
                                    yield part["text"]
                        
            except Exception as e:
                yield f"❌ Gemini API调用失败: {str(e)}"
    
    async def call_zhipu(
        self,
        messages: List[Dict[str, str]],
        model: str,
        api_key: str,
        api_base: Optional[str] = None,
        stream: bool = True
    ) -> AsyncGenerator[str, None]:
        """调用智谱AI API"""
        url = f"{api_base or 'https://open.bigmodel.cn/api/paas/v4'}/chat/completions"
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": model,
            "messages": messages,
            "stream": stream,
            "temperature": 0.7,
            "max_tokens": 2000
        }
        
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                if stream:
                    async with client.stream("POST", url, headers=headers, json=data) as response:
                        if response.status_code != 200:
                            error_text = await response.aread()
                            raise Exception(f"API调用失败: {response.status_code} - {error_text.decode()}")
                        
                        async for line in response.aiter_lines():
                            if line.startswith("data: "):
                                line = line[6:]
                                if line.strip() == "[DONE]":
                                    break
                                try:
                                    chunk = json.loads(line)
                                    if "choices" in chunk and len(chunk["choices"]) > 0:
                                        delta = chunk["choices"][0].get("delta", {})
                                        if "content" in delta:
                                            yield delta["content"]
                                except json.JSONDecodeError:
                                    continue
                else:
                    response = await client.post(url, headers=headers, json=data)
                    if response.status_code != 200:
                        raise Exception(f"API调用失败: {response.status_code} - {response.text}")
                    
                    result = response.json()
                    if "choices" in result and len(result["choices"]) > 0:
                        yield result["choices"][0]["message"]["content"]
                        
            except Exception as e:
                yield f"❌ 智谱AI API调用失败: {str(e)}"
    
    async def call_qwen(
        self,
        messages: List[Dict[str, str]],
        model: str,
        api_key: str,
        api_base: Optional[str] = None,
        stream: bool = True
    ) -> AsyncGenerator[str, None]:
        """调用Qwen API"""
        url = f"{api_base or 'https://dashscope.aliyuncs.com/api/v1'}/services/aigc/text-generation/generation"
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": model,
            "input": {"messages": messages},
            "parameters": {
                "temperature": 0.7,
                "max_tokens": 2000,
                "incremental_output": stream
            }
        }
        
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                response = await client.post(url, headers=headers, json=data)
                if response.status_code != 200:
                    raise Exception(f"API调用失败: {response.status_code} - {response.text}")
                
                result = response.json()
                if "output" in result and "text" in result["output"]:
                    yield result["output"]["text"]
                else:
                    yield "❌ Qwen API返回格式异常"
                        
            except Exception as e:
                yield f"❌ Qwen API调用失败: {str(e)}"

    async def call_local_model(
        self,
        messages: List[Dict[str, str]],
        model: str,
        api_key: str,
        api_base: str,
        stream: bool = True
    ) -> AsyncGenerator[str, None]:
        """调用本地模型API"""
        url = f"{api_base}/chat/completions"
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        data = {
            "model": model,
            "messages": messages,
            "stream": stream,
            "temperature": 0.7,
            "max_tokens": 2000
        }

        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                if stream:
                    async with client.stream("POST", url, headers=headers, json=data) as response:
                        if response.status_code != 200:
                            error_text = await response.aread()
                            raise Exception(f"API调用失败: {response.status_code} - {error_text.decode()}")

                        async for line in response.aiter_lines():
                            if line.startswith("data: "):
                                line = line[6:]
                                if line.strip() == "[DONE]":
                                    break
                                try:
                                    chunk = json.loads(line)
                                    if "choices" in chunk and len(chunk["choices"]) > 0:
                                        delta = chunk["choices"][0].get("delta", {})
                                        if "content" in delta:
                                            yield delta["content"]
                                except json.JSONDecodeError:
                                    continue
                else:
                    response = await client.post(url, headers=headers, json=data)
                    if response.status_code != 200:
                        raise Exception(f"API调用失败: {response.status_code} - {response.text}")

                    result = response.json()
                    if "choices" in result and len(result["choices"]) > 0:
                        yield result["choices"][0]["message"]["content"]

            except Exception as e:
                yield f"❌ 本地模型API调用失败: {str(e)}"

    async def chat_with_ai(
        self,
        provider: str,
        model: str,
        messages: List[Dict[str, str]],
        api_key: str,
        api_base: Optional[str] = None,
        stream: bool = True
    ) -> AsyncGenerator[str, None]:
        """统一的AI聊天接口"""
        if not api_key:
            yield "❌ 请先配置API Key"
            return

        try:
            if provider == "OpenAI":
                async for chunk in self.call_openai(messages, model, api_key, api_base, stream):
                    yield chunk
            elif provider == "Claude":
                async for chunk in self.call_claude(messages, model, api_key, api_base, stream):
                    yield chunk
            elif provider == "Gemini":
                async for chunk in self.call_gemini(messages, model, api_key, api_base, stream):
                    yield chunk
            elif provider == "智谱AI":
                async for chunk in self.call_zhipu(messages, model, api_key, api_base, stream):
                    yield chunk
            elif provider == "Qwen":
                async for chunk in self.call_qwen(messages, model, api_key, api_base, stream):
                    yield chunk
            elif provider == "本地模型":
                if not api_base:
                    yield "❌ 本地模型需要配置API Base URL"
                    return
                async for chunk in self.call_local_model(messages, model, api_key, api_base, stream):
                    yield chunk
            else:
                yield f"❌ 不支持的AI提供商: {provider}"

        except Exception as e:
            yield f"❌ AI调用失败: {str(e)}"


# 全局AI客户端实例
ai_client = AIClient()
